# -*- coding: gbk -*-
"""
ZZcalc.py - 技术指标计算模块
接收 ZZfetch.py 获取的K线数据并计算常见技术指标

主要功能：
1. 计算MACD指标（DIF、DEA、MACD柱状图）
2. 计算KDJ指标（RSV、K值、D值、J值）
3. 计算RSI指标（6日、12日、24日）
4. 计算布林带指标（20日均线、上下轨）
5. 计算移动平均线（5日、10日、20日、60日）
6. 计算成交量均线（5日、10日）

作者：基于技术指标计算需求开发
日期：2025-01-28
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime
import json

# 导入数据获取模块
try:
    from ZZfetch import get_fetcher, connect_miniqmt, disconnect_miniqmt
    ZZFETCH_AVAILABLE = True
except ImportError:
    ZZFETCH_AVAILABLE = False
    print("警告: ZZfetch模块未找到，无法自动获取K线数据")


class TechnicalIndicators:
    """技术指标计算器"""
    
    def __init__(self):
        """初始化技术指标计算器"""
        self.data = None
        self.indicators = {}
    
    def load_data_from_dataframe(self, df):
        """从DataFrame加载K线数据"""
        if df is None or df.empty:
            print("错误: 输入数据为空")
            return False
        
        # 验证必要的列是否存在
        required_columns = ['日期', '开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误: 缺少必要的数据列: {missing_columns}")
            return False
        
        # 复制数据并确保按日期排序
        self.data = df.copy()
        self.data = self.data.sort_values('日期', ascending=True).reset_index(drop=True)
        
        # 验证数据有效性
        if len(self.data) < 30:
            print(f"警告: 数据量较少({len(self.data)}条)，可能影响指标计算准确性")
        
        print(f"? 成功加载K线数据: {len(self.data)} 条记录")
        return True
    
    def load_data_from_json(self, json_data):
        """从JSON数据加载K线数据"""
        try:
            if isinstance(json_data, str):
                data_dict = json.loads(json_data)
            else:
                data_dict = json_data
            
            df = pd.DataFrame(data_dict)
            return self.load_data_from_dataframe(df)
        
        except Exception as e:
            print(f"错误: JSON数据解析失败: {e}")
            return False
    
    def calculate_ema(self, data, period):
        """计算指数移动平均线（EMA）"""
        if len(data) < period:
            return pd.Series([np.nan] * len(data), index=data.index)
        
        alpha = 2.0 / (period + 1)
        ema = data.ewm(alpha=alpha, adjust=False).mean()
        return ema
    
    def calculate_sma(self, data, period):
        """计算简单移动平均线（SMA）"""
        return data.rolling(window=period, min_periods=1).mean()
    
    def calculate_macd(self, fast_period=12, slow_period=26, signal_period=9):
        """计算MACD指标"""
        if self.data is None or len(self.data) < slow_period:
            print("错误: 数据不足，无法计算MACD")
            return None
        
        close_prices = self.data['收盘']
        
        # 计算快线和慢线EMA
        ema_fast = self.calculate_ema(close_prices, fast_period)
        ema_slow = self.calculate_ema(close_prices, slow_period)
        
        # 计算DIF（快线-慢线）
        dif = ema_fast - ema_slow
        
        # 计算DEA（DIF的信号线）
        dea = self.calculate_ema(dif, signal_period)
        
        # 计算MACD柱状图
        macd_histogram = 2 * (dif - dea)
        
        macd_result = {
            'DIF': dif,
            'DEA': dea,
            'MACD': macd_histogram,
            'EMA_FAST': ema_fast,
            'EMA_SLOW': ema_slow
        }
        
        self.indicators['MACD'] = macd_result
        return macd_result
    
    def calculate_kdj(self, period=9, k_period=3, d_period=3):
        """计算KDJ指标"""
        if self.data is None or len(self.data) < period:
            print("错误: 数据不足，无法计算KDJ")
            return None
        
        high_prices = self.data['最高']
        low_prices = self.data['最低']
        close_prices = self.data['收盘']
        
        # 计算RSV（未成熟随机值）
        lowest_low = low_prices.rolling(window=period, min_periods=1).min()
        highest_high = high_prices.rolling(window=period, min_periods=1).max()
        
        rsv = 100 * (close_prices - lowest_low) / (highest_high - lowest_low)
        rsv = rsv.fillna(50)  # 填充NaN值
        
        # 计算K值（RSV的移动平均）
        k_values = []
        k_prev = 50  # K值初始值
        
        for rsv_val in rsv:
            if pd.isna(rsv_val):
                k_val = k_prev
            else:
                k_val = (2 * k_prev + rsv_val) / 3
            k_values.append(k_val)
            k_prev = k_val
        
        k_series = pd.Series(k_values, index=rsv.index)
        
        # 计算D值（K值的移动平均）
        d_values = []
        d_prev = 50  # D值初始值
        
        for k_val in k_values:
            d_val = (2 * d_prev + k_val) / 3
            d_values.append(d_val)
            d_prev = d_val
        
        d_series = pd.Series(d_values, index=rsv.index)
        
        # 计算J值
        j_series = 3 * k_series - 2 * d_series
        
        kdj_result = {
            'RSV': rsv,
            'K': k_series,
            'D': d_series,
            'J': j_series
        }
        
        self.indicators['KDJ'] = kdj_result
        return kdj_result
    
    def calculate_rsi(self, periods=[6, 12, 24]):
        """计算RSI指标"""
        if self.data is None or len(self.data) < max(periods):
            print("错误: 数据不足，无法计算RSI")
            return None
        
        close_prices = self.data['收盘']
        
        # 计算价格变化
        price_changes = close_prices.diff()
        
        rsi_result = {}
        
        for period in periods:
            # 分离上涨和下跌
            gains = price_changes.where(price_changes > 0, 0)
            losses = -price_changes.where(price_changes < 0, 0)
            
            # 计算平均收益和平均损失
            avg_gains = gains.rolling(window=period, min_periods=1).mean()
            avg_losses = losses.rolling(window=period, min_periods=1).mean()
            
            # 计算RSI
            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))
            
            # 处理除零情况
            rsi = rsi.fillna(50)
            
            rsi_result[f'RSI_{period}'] = rsi
        
        self.indicators['RSI'] = rsi_result
        return rsi_result
    
    def calculate_bollinger_bands(self, period=20, std_dev=2):
        """计算布林带指标"""
        if self.data is None or len(self.data) < period:
            print("错误: 数据不足，无法计算布林带")
            return None
        
        close_prices = self.data['收盘']
        
        # 计算中轨（简单移动平均线）
        middle_band = self.calculate_sma(close_prices, period)
        
        # 计算标准差
        std = close_prices.rolling(window=period, min_periods=1).std()
        
        # 计算上轨和下轨
        upper_band = middle_band + (std_dev * std)
        lower_band = middle_band - (std_dev * std)
        
        bollinger_result = {
            'UPPER': upper_band,
            'MIDDLE': middle_band,
            'LOWER': lower_band,
            'STD': std
        }
        
        self.indicators['BOLLINGER'] = bollinger_result
        return bollinger_result
    
    def calculate_moving_averages(self, periods=[5, 10, 20, 60]):
        """计算移动平均线"""
        if self.data is None:
            print("错误: 数据不足，无法计算移动平均线")
            return None
        
        close_prices = self.data['收盘']
        ma_result = {}
        
        for period in periods:
            if len(self.data) >= period:
                ma_result[f'MA_{period}'] = self.calculate_sma(close_prices, period)
            else:
                print(f"警告: 数据不足，跳过MA{period}计算")
        
        self.indicators['MA'] = ma_result
        return ma_result
    
    def calculate_volume_ma(self, periods=[5, 10]):
        """计算成交量均线"""
        if self.data is None:
            print("错误: 数据不足，无法计算成交量均线")
            return None
        
        volume = self.data['成交量']
        volume_ma_result = {}
        
        for period in periods:
            if len(self.data) >= period:
                volume_ma_result[f'VOL_MA_{period}'] = self.calculate_sma(volume, period)
            else:
                print(f"警告: 数据不足，跳过VOL_MA{period}计算")
        
        self.indicators['VOLUME_MA'] = volume_ma_result
        return volume_ma_result

    def calculate_all_indicators(self):
        """计算所有技术指标"""
        if self.data is None:
            print("错误: 未加载数据，无法计算指标")
            return None

        print("正在计算技术指标...")

        # 计算各项指标
        macd = self.calculate_macd()
        kdj = self.calculate_kdj()
        rsi = self.calculate_rsi()
        bollinger = self.calculate_bollinger_bands()
        ma = self.calculate_moving_averages()
        volume_ma = self.calculate_volume_ma()

        print("? 所有技术指标计算完成")

        return {
            'MACD': macd,
            'KDJ': kdj,
            'RSI': rsi,
            'BOLLINGER': bollinger,
            'MA': ma,
            'VOLUME_MA': volume_ma
        }

    def get_latest_values(self):
        """获取最新的指标值"""
        if not self.indicators:
            print("错误: 未计算指标，请先调用计算方法")
            return None

        latest_values = {}

        # 获取最新日期
        if self.data is not None and len(self.data) > 0:
            latest_date = self.data['日期'].iloc[-1]
            latest_values['日期'] = latest_date

            # 获取最新价格数据
            latest_values['收盘价'] = self.data['收盘'].iloc[-1]
            latest_values['成交量'] = self.data['成交量'].iloc[-1]

        # 获取各指标最新值
        for indicator_name, indicator_data in self.indicators.items():
            if indicator_data:
                latest_values[indicator_name] = {}
                for key, series in indicator_data.items():
                    if isinstance(series, pd.Series) and len(series) > 0:
                        latest_values[indicator_name][key] = series.iloc[-1]

        return latest_values

    def get_recent_values(self, periods=5):
        """获取最近N个周期的指标值"""
        if not self.indicators or self.data is None:
            print("错误: 未计算指标或数据为空")
            return None

        recent_values = {}
        data_length = len(self.data)

        # 确保不超过数据长度
        actual_periods = min(periods, data_length)

        # 获取最近N个周期的日期
        recent_dates = self.data['日期'].iloc[-actual_periods:].tolist()
        recent_values['日期'] = recent_dates

        # 获取最近N个周期的价格和成交量
        recent_values['收盘价'] = self.data['收盘'].iloc[-actual_periods:].tolist()
        recent_values['成交量'] = self.data['成交量'].iloc[-actual_periods:].tolist()

        # 获取各指标最近N个周期的值
        for indicator_name, indicator_data in self.indicators.items():
            if indicator_data:
                recent_values[indicator_name] = {}
                for key, series in indicator_data.items():
                    if isinstance(series, pd.Series) and len(series) >= actual_periods:
                        recent_values[indicator_name][key] = series.iloc[-actual_periods:].tolist()

        return recent_values

    def format_output(self, stock_code="", periods=5):
        """格式化输出结果 - 显示最近N个周期的指标值"""
        if not self.indicators:
            return "未计算任何指标"

        recent = self.get_recent_values(periods)

        if not recent:
            return "无法获取指标数据"

        output = []
        output.append("=" * 80)
        output.append(f"技术指标分析结果 - {stock_code} (最近{periods}个周期)")
        output.append("=" * 80)

        # 基本信息
        if '日期' in recent and recent['日期']:
            latest_date = recent['日期'][-1]
            output.append(f"最新数据日期: {latest_date}")
        if '收盘价' in recent and recent['收盘价']:
            latest_price = recent['收盘价'][-1]
            output.append(f"最新收盘价: {latest_price:.2f}")
        if '成交量' in recent and recent['成交量']:
            latest_volume = recent['成交量'][-1]
            output.append(f"最新成交量: {latest_volume:,.0f}")

        output.append("")

        # MACD指标表格
        if 'MACD' in recent:
            output.append("【MACD指标】（最近5个周期）")
            macd_data = recent['MACD']
            if all(key in macd_data for key in ['DIF', 'DEA', 'MACD']):
                output.append("  日期        DIF      DEA      MACD")
                output.append("  " + "-" * 40)

                dates = recent['日期']
                dif_values = macd_data['DIF']
                dea_values = macd_data['DEA']
                macd_values = macd_data['MACD']

                # 倒序显示（最新的在上面）
                for i in range(len(dates)-1, -1, -1):
                    date_str = dates[i].strftime('%Y-%m-%d') if hasattr(dates[i], 'strftime') else str(dates[i])
                    output.append(f"  {date_str}  {dif_values[i]:7.4f}  {dea_values[i]:7.4f}  {macd_values[i]:8.4f}")
            output.append("")

        # KDJ指标表格
        if 'KDJ' in recent:
            output.append("【KDJ指标】（最近5个周期）")
            kdj_data = recent['KDJ']
            if all(key in kdj_data for key in ['K', 'D', 'J']):
                output.append("  日期        K值     D值     J值")
                output.append("  " + "-" * 35)

                dates = recent['日期']
                k_values = kdj_data['K']
                d_values = kdj_data['D']
                j_values = kdj_data['J']

                # 倒序显示（最新的在上面）
                for i in range(len(dates)-1, -1, -1):
                    date_str = dates[i].strftime('%Y-%m-%d') if hasattr(dates[i], 'strftime') else str(dates[i])
                    output.append(f"  {date_str}  {k_values[i]:6.2f}  {d_values[i]:6.2f}  {j_values[i]:7.2f}")
            output.append("")

        # RSI指标表格
        if 'RSI' in recent:
            output.append("【RSI指标】（最近5个周期）")
            rsi_data = recent['RSI']

            # 获取所有RSI周期
            rsi_periods = sorted([key for key in rsi_data.keys() if key.startswith('RSI_')])

            if rsi_periods:
                # 构建表头
                header = "  日期      "
                for period_key in rsi_periods:
                    period = period_key.split('_')[1]
                    header += f"  RSI{period:>2}"
                output.append(header)
                output.append("  " + "-" * (len(header) - 2))

                dates = recent['日期']

                # 倒序显示（最新的在上面）
                for i in range(len(dates)-1, -1, -1):
                    date_str = dates[i].strftime('%Y-%m-%d') if hasattr(dates[i], 'strftime') else str(dates[i])
                    row = f"  {date_str}"
                    for period_key in rsi_periods:
                        if period_key in rsi_data and i < len(rsi_data[period_key]):
                            row += f"  {rsi_data[period_key][i]:6.2f}"
                        else:
                            row += f"  {'N/A':>6}"
                    output.append(row)
            output.append("")

        # 布林带指标表格
        if 'BOLLINGER' in recent:
            output.append("【布林带指标】（最近5个周期）")
            bollinger_data = recent['BOLLINGER']
            if all(key in bollinger_data for key in ['UPPER', 'MIDDLE', 'LOWER']):
                output.append("  日期        上轨     中轨     下轨")
                output.append("  " + "-" * 40)

                dates = recent['日期']
                upper_values = bollinger_data['UPPER']
                middle_values = bollinger_data['MIDDLE']
                lower_values = bollinger_data['LOWER']

                # 倒序显示（最新的在上面）
                for i in range(len(dates)-1, -1, -1):
                    date_str = dates[i].strftime('%Y-%m-%d') if hasattr(dates[i], 'strftime') else str(dates[i])
                    output.append(f"  {date_str}  {upper_values[i]:7.2f}  {middle_values[i]:7.2f}  {lower_values[i]:7.2f}")
            output.append("")

        # 移动平均线表格
        if 'MA' in recent:
            output.append("【移动平均线】（最近5个周期）")
            ma_data = recent['MA']

            # 获取所有MA周期
            ma_periods = sorted([key for key in ma_data.keys() if key.startswith('MA_')],
                               key=lambda x: int(x.split('_')[1]))

            if ma_periods:
                # 构建表头
                header = "  日期      "
                for period_key in ma_periods:
                    period = period_key.split('_')[1]
                    header += f"   MA{period:>2}"
                output.append(header)
                output.append("  " + "-" * (len(header) - 2))

                dates = recent['日期']

                # 倒序显示（最新的在上面）
                for i in range(len(dates)-1, -1, -1):
                    date_str = dates[i].strftime('%Y-%m-%d') if hasattr(dates[i], 'strftime') else str(dates[i])
                    row = f"  {date_str}"
                    for period_key in ma_periods:
                        if period_key in ma_data and i < len(ma_data[period_key]):
                            row += f"  {ma_data[period_key][i]:7.2f}"
                        else:
                            row += f"  {'N/A':>7}"
                    output.append(row)
            output.append("")

        # 成交量均线表格
        if 'VOLUME_MA' in recent:
            output.append("【成交量均线】（最近5个周期）")
            vol_ma_data = recent['VOLUME_MA']

            # 获取所有成交量MA周期
            vol_ma_periods = sorted([key for key in vol_ma_data.keys() if key.startswith('VOL_MA_')],
                                   key=lambda x: int(x.split('_')[2]))

            if vol_ma_periods:
                # 构建表头
                header = "  日期      "
                for period_key in vol_ma_periods:
                    period = period_key.split('_')[2]
                    header += f"     VOL_MA{period}"
                output.append(header)
                output.append("  " + "-" * (len(header) - 2))

                dates = recent['日期']

                # 倒序显示（最新的在上面）
                for i in range(len(dates)-1, -1, -1):
                    date_str = dates[i].strftime('%Y-%m-%d') if hasattr(dates[i], 'strftime') else str(dates[i])
                    row = f"  {date_str}"
                    for period_key in vol_ma_periods:
                        if period_key in vol_ma_data and i < len(vol_ma_data[period_key]):
                            row += f"  {vol_ma_data[period_key][i]:12,.0f}"
                        else:
                            row += f"  {'N/A':>12}"
                    output.append(row)
            output.append("")

        output.append("=" * 80)

        return "\n".join(output)


# 便捷函数接口
def calculate_indicators_from_dataframe(df, stock_code=""):
    """从DataFrame计算技术指标的便捷函数"""
    calculator = TechnicalIndicators()

    if not calculator.load_data_from_dataframe(df):
        return None

    indicators = calculator.calculate_all_indicators()

    return {
        'calculator': calculator,
        'indicators': indicators,
        'latest_values': calculator.get_latest_values(),
        'recent_values': calculator.get_recent_values(),
        'formatted_output': calculator.format_output(stock_code)
    }


def calculate_indicators_from_json(json_data, stock_code=""):
    """从JSON数据计算技术指标的便捷函数"""
    calculator = TechnicalIndicators()

    if not calculator.load_data_from_json(json_data):
        return None

    indicators = calculator.calculate_all_indicators()

    return {
        'calculator': calculator,
        'indicators': indicators,
        'latest_values': calculator.get_latest_values(),
        'recent_values': calculator.get_recent_values(),
        'formatted_output': calculator.format_output(stock_code)
    }


def calculate_indicators_from_stock_code(stock_code, count=250):
    """从股票代码自动获取数据并计算技术指标的便捷函数"""
    if not ZZFETCH_AVAILABLE:
        print("错误: ZZfetch模块不可用，无法自动获取数据")
        return None

    try:
        # 获取数据获取器
        fetcher = get_fetcher()

        # 连接MiniQMT
        if not fetcher.connect_miniqmt():
            print("错误: 无法连接MiniQMT，请检查QMT客户端是否运行")
            return None

        # 获取K线数据
        print(f"正在获取股票 {stock_code} 的K线数据...")
        df = fetcher.get_stock_daily_data(stock_code, count)

        if df is None or df.empty:
            print(f"错误: 无法获取股票 {stock_code} 的K线数据")
            return None

        # 计算技术指标
        result = calculate_indicators_from_dataframe(df, stock_code)

        return result

    except Exception as e:
        print(f"获取数据或计算指标时出错: {e}")
        return None


def main():
    """主函数 - 用户交互界面"""
    print("=" * 60)
    print("ZZcalc.py - 技术指标计算工具")
    print("=" * 60)
    print("功能说明:")
    print("1. 输入6位股票代码，自动获取K线数据并计算技术指标")
    print("2. 支持计算MACD、KDJ、RSI、布林带、移动平均线、成交量均线")
    print("3. 以表格形式显示最近5个周期的指标值")
    print("=" * 60)

    if not ZZFETCH_AVAILABLE:
        print("警告: ZZfetch模块不可用，只能使用数据输入模式")
        print("请确保ZZfetch.py文件存在且可正常导入")
        return

    # 主循环
    while True:
        print("\n" + "-" * 40)
        code_input = input("请输入6位股票代码 (输入 'quit' 退出): ").strip()

        if code_input.lower() in ['quit', 'exit', 'q']:
            break

        if not code_input:
            continue

        # 验证股票代码格式
        if len(code_input) != 6 or not code_input.isdigit():
            print("错误: 请输入6位数字股票代码")
            continue

        try:
            # 计算技术指标
            result = calculate_indicators_from_stock_code(code_input)

            if result:
                # 显示结果
                print(result['formatted_output'])

                # 询问是否保存结果
                save_choice = input("\n是否保存结果到文件? (y/n): ").strip().lower()
                if save_choice in ['y', 'yes']:
                    filename = f"技术指标_{code_input}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                    try:
                        with open(filename, 'w', encoding='gbk') as f:
                            f.write(result['formatted_output'])
                        print(f"? 结果已保存到文件: {filename}")
                    except Exception as e:
                        print(f"保存文件失败: {e}")
            else:
                print("计算失败，请检查股票代码是否正确")

        except Exception as e:
            print(f"处理过程中出错: {e}")
            import traceback
            traceback.print_exc()

    # 断开连接
    try:
        disconnect_miniqmt()
    except:
        pass

    print("\n程序退出，感谢使用！")


def test_indicators():
    """测试技术指标计算功能"""
    print("=" * 60)
    print("ZZcalc.py - 技术指标计算测试")
    print("=" * 60)

    if not ZZFETCH_AVAILABLE:
        print("错误: ZZfetch模块不可用，无法进行测试")
        return

    # 测试股票代码
    test_codes = ["000001", "600000", "000002"]

    for code in test_codes:
        print(f"\n{'='*40}")
        print(f"测试股票代码: {code}")
        print(f"{'='*40}")

        try:
            result = calculate_indicators_from_stock_code(code, 100)

            if result:
                print("? 技术指标计算成功")
                print("\n最新指标值:")
                latest = result['latest_values']

                # 显示关键指标摘要
                if 'MACD' in latest:
                    macd = latest['MACD']
                    print(f"  MACD - DIF: {macd.get('DIF', 'N/A'):.4f}, DEA: {macd.get('DEA', 'N/A'):.4f}")

                if 'KDJ' in latest:
                    kdj = latest['KDJ']
                    print(f"  KDJ - K: {kdj.get('K', 'N/A'):.2f}, D: {kdj.get('D', 'N/A'):.2f}, J: {kdj.get('J', 'N/A'):.2f}")

                if 'RSI' in latest:
                    rsi = latest['RSI']
                    rsi_6 = rsi.get('RSI_6', 'N/A')
                    print(f"  RSI6: {rsi_6:.2f}" if isinstance(rsi_6, (int, float)) else f"  RSI6: {rsi_6}")
            else:
                print("? 技术指标计算失败")

        except Exception as e:
            print(f"测试 {code} 时出错: {e}")

    print("\n测试完成！")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_indicators()
        else:
            main()
    else:
        main()
