# -*- coding: gbk -*-

import os
import re

class DataConfig:
    
    TDX_BASE_DIRS = [
        r"C:\new_tdx",
        r"D:\new_tdx",
        r"C:\tdx",
        r"D:\tdx",
        r"C:\Program Files\new_tdx",
        r"D:\Program Files\new_tdx"
    ]

    # 资金面数据目录配置
    TDX_CAPITAL_DIRS = [
        r"D:\stock\tdxdata",  # 默认配置目录
        r"D:\new_tdx\T0002\hq_cache",
        r"C:\new_tdx\T0002\hq_cache",
        r"D:\tdx\T0002\hq_cache",
        r"C:\tdx\T0002\hq_cache"
    ]
    
    MINIQMT_CONFIG = {
        'host': '127.0.0.1',
        'port': 58610,
        'username': '',
        'password': '',
        'session_id': 0,
        'enable_cmd_cache': True,
        'enable_heartbeat': True
    }
    
    DEFAULT_PARAMS = {
        'daily_count': 250,
        'weekly_count': 100,
        'min30_count': 500,
        'min5_count': 1000,
        'capital_days': 5,
        'retry_times': 3,
        'retry_delay': 1.0,
        'timeout': 30
    }
    
    ERROR_CONFIG = {
        'enable_retry': True,
        'max_retry_times': 3,
        'retry_delay': 1.0,
        'timeout': 30,
        'log_level': 'INFO'
    }
    
    DATA_FORMAT = {
        'kline_columns': ['date', 'open', 'high', 'low', 'close', 'volume', 'amount'],
        'capital_columns': ['date', 'main_flow', 'main_buy', 'inner_volume', 'outer_volume'],
        'date_format': '%Y-%m-%d',
        'datetime_format': '%Y-%m-%d %H:%M:%S',
        'encoding': 'gbk'
    }
    
    CODE_PATTERNS = {
        'block_codes': [
            r'^880[0-9]{3}$',
            r'^881[0-9]{3}$',
            r'^399[0-9]{3}$',
        ],
        'stock_codes': [
            r'^[0-9]{6}$',
        ]
    }
    
    DEBUG_CONFIG = {
        'enable_debug': True,
        'show_data_sample': True,
        'sample_rows': 5,
        'show_timing': True,
        'show_data_info': True
    }
    
    @classmethod
    def get_tdx_base_dir(cls):
        for dir_path in cls.TDX_BASE_DIRS:
            if os.path.exists(dir_path):
                return dir_path
        return None
    
    @classmethod
    def get_tdx_capital_dir(cls):
        # 优先检查配置的资金面数据目录
        for dir_path in cls.TDX_CAPITAL_DIRS:
            if os.path.exists(dir_path):
                return dir_path
        return None
    
    @classmethod
    def is_stock_code(cls, code):
        if not code or len(code) != 6:
            return False
        if cls.is_block_code(code):
            return False
        return any(re.match(pattern, code) for pattern in cls.CODE_PATTERNS['stock_codes'])
    
    @classmethod
    def is_block_code(cls, code):
        if not code or len(code) != 6:
            return False
        return any(re.match(pattern, code) for pattern in cls.CODE_PATTERNS['block_codes'])
    
    @classmethod
    def clean_stock_code(cls, code):
        """清理股票代码，与原始程序逻辑保持一致"""
        code_str = str(code).strip()

        # 移除常见前缀（与原始程序一致）
        prefixes_to_remove = ['SH', 'SZ', 'sh', 'sz']
        for prefix in prefixes_to_remove:
            if code_str.startswith(prefix):
                code_str = code_str[len(prefix):]
                break

        # 只保留数字字符
        code_str = ''.join(filter(str.isdigit, code_str))

        # 确保是6位数字
        if len(code_str) == 6:
            return code_str

        return code_str
