# -*- coding: gbk -*-
import pandas as pd
import numpy as np
import sys
import os
import re
import locale
from datetime import datetime, timedelta
import time  # 添加time模块用于延迟
from scipy.signal import argrelextrema  # 添加寻找局部极值的函数

# 添加项目目录到路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入akshare用于获取新闻
try:
    import akshare as ak
except ImportError as e:
    print(f"无法导入akshare: {e}，将使用模拟新闻数据")
    ak = None

# 导入matplotlib用于绘图
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib import font_manager
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
except ImportError as e:
    print(f"无法导入matplotlib: {e}")
    plt = None

# === 基础技术指标计算函数 ===

def calc_ema(series, period):
    """计算EMA（指数移动平均）"""
    return series.ewm(span=period, adjust=False).mean()

def calc_macd(close, fastperiod=12, slowperiod=26, signalperiod=9):
    """
    计算MACD指标
    
    参数:
        close: 收盘价序列
        fastperiod: 快速EMA周期，默认12
        slowperiod: 慢速EMA周期，默认26  
        signalperiod: 信号线EMA周期，默认9
    
    返回:
        dif: DIF线（快速EMA - 慢速EMA）
        dea: DEA线（DIF的9日EMA）
        macd: MACD柱状图（(DIF - DEA) * 2）
    """
    fast_ema = calc_ema(close, fastperiod)
    slow_ema = calc_ema(close, slowperiod)
    dif = fast_ema - slow_ema
    dea = calc_ema(dif, signalperiod)
    macd = (dif - dea) * 2
    return dif, dea, macd

def calc_ma(series, period):
    """计算简单移动平均线"""
    return series.rolling(window=period).mean()

def calc_kdj(high, low, close, fastk_period=9, slowk_period=3, slowd_period=3):
    """
    计算KDJ指标（完全按照通达信公式）
    
    参数:
        high: 最高价序列
        low: 最低价序列  
        close: 收盘价序列
        fastk_period: RSV计算周期，默认9
        slowk_period: K值平滑参数，默认3（对应1/3）
        slowd_period: D值平滑参数，默认3（对应1/3）
    
    公式:
        RSV = (CLOSE - LLV(LOW, 9)) / (HHV(HIGH, 9) - LLV(LOW, 9)) * 100
        K = SMA(RSV, 3, 1)  
        D = SMA(K, 3, 1)
        J = 3K - 2D
    """
    # 计算RSV
    low_min = low.rolling(window=fastk_period).min()
    high_max = high.rolling(window=fastk_period).max()
    rsv = 100 * ((close - low_min) / (high_max - low_min))
    
    # 初始化K、D线
    k = pd.Series(index=rsv.index, dtype=float)
    d = pd.Series(index=rsv.index, dtype=float)
    
    # 设置初始值
    k.iloc[0] = 50.0 if pd.isna(rsv.iloc[0]) else rsv.iloc[0]
    d.iloc[0] = 50.0 if pd.isna(k.iloc[0]) else k.iloc[0]
    
    # 使用SMA公式：今日K值 = 2/3 * 昨日K值 + 1/3 * 今日RSV值
    for i in range(1, len(rsv)):
        rsv_val = rsv.iloc[i] if not pd.isna(rsv.iloc[i]) else k.iloc[i-1]
        k.iloc[i] = (2/3) * k.iloc[i-1] + (1/3) * rsv_val
        d.iloc[i] = (2/3) * d.iloc[i-1] + (1/3) * k.iloc[i]
        
    # 计算J线：J = 3K - 2D
    j = 3 * k - 2 * d
    
    return k, d, j

def calc_bias(close, timeperiod=6):
    """
    计算BIAS指标（乖离率）
    
    公式: BIAS = (CLOSE - MA) / MA * 100
    """
    ma = calc_ma(close, timeperiod)
    bias = (close - ma) / ma * 100
    return bias

def calc_bollinger(close, timeperiod=20, nbdevup=2, nbdevdn=2):
    """
    计算布林带指标
    
    参数:
        close: 收盘价序列
        timeperiod: 移动平均周期，默认20
        nbdevup: 上轨标准差倍数，默认2
        nbdevdn: 下轨标准差倍数，默认2
    """
    ma = calc_ma(close, timeperiod)
    std = close.rolling(window=timeperiod).std()
    upper = ma + nbdevup * std
    lower = ma - nbdevdn * std
    return upper, ma, lower

def calc_rsi(close, period=14):
    """
    计算RSI指标（修正版本，使用威尔德平滑方法）
    
    公式:
        RS = 平均上涨幅度 / 平均下跌幅度
        RSI = 100 - 100 / (1 + RS)
    """
    delta = close.diff()
    
    # 分离涨跌
    gain = delta.where(delta > 0, 0.0)
    loss = -delta.where(delta < 0, 0.0)
    
    # 计算威尔德平均值
    avg_gain = gain.ewm(alpha=1/period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/period, adjust=False).mean()
    
    # 避免除零错误
    rs = avg_gain / avg_loss.replace(0, np.inf)
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

# === 技术指标分析函数 ===

def analyze_macd_score(dif_values, macd_values):
    """
    分析MACD指标并计算得分（修正版本）
    
    参数:
        dif_values: DIF值列表 (最新的在前)
        macd_values: MACD值列表 (最新的在前)
    
    返回:
        score: 总得分
        comments: 详细分析评论列表
    """
    score = 0
    comments = []
    
    if len(dif_values) < 2 or len(macd_values) < 2:
        comments.append("数据不足，无法进行MACD分析")
        return 0, comments
    
    # 获取最新和前一周期的值
    latest_dif = dif_values[0]
    prev_dif = dif_values[1]
    latest_macd = macd_values[0]
    prev_macd = macd_values[1]
    
    # 计算DIF值的振幅范围
    if len(dif_values) >= 5:
        dif_range = max(dif_values[:5]) - min(dif_values[:5])
        threshold = max(dif_range * 0.1, abs(latest_dif) * 0.05)  # 动态阈值
    else:
        threshold = abs(latest_dif) * 0.1
    
    # 1. DIF上涨判断
    if latest_dif > prev_dif:
        score += 1
        comments.append("DIF上涨，加1分")
    
    # 2. 绿柱缩短判断（MACD < 0 且在变大）
    if latest_macd > prev_macd and latest_macd < 0:
        score += 1
        comments.append("绿柱缩短，加1分")
    
    # 3. 红柱变长判断（MACD > 0 且在变大）
    if latest_macd > prev_macd and latest_macd > 0:
        score += 1
        comments.append("红柱变长，加1分")
    
    # 4. DIF大于0轴判断（修正逻辑）
    if latest_dif > 0 and latest_dif > threshold:
        score += 1
        comments.append("DIF大于0轴，加1分")
    
    # 5. DIF位于0轴附近判断
    if abs(latest_dif) < threshold:
        score += 1
        comments.append("DIF位于0轴附近，加1分")
    
    # 6. MACD金叉判断（修正：检查最近3个周期内的金叉）
    if len(macd_values) >= 3:
        macd_golden_cross = False
        for i in range(min(3, len(macd_values)-1)):
            if macd_values[i] > 0 and macd_values[i+1] <= 0:
                macd_golden_cross = True
                break
        if macd_golden_cross:
            score += 2
            comments.append("MACD金叉，加2分")
    
    # 7. DIF上穿0轴判断（修正：检查最近3个周期内的上穿）
    if len(dif_values) >= 3:
        dif_cross_zero = False
        for i in range(min(3, len(dif_values)-1)):
            if dif_values[i] > 0 and dif_values[i+1] <= 0:
                dif_cross_zero = True
                break
        if dif_cross_zero:
            score += 1
            comments.append("DIF上穿0轴，加1分")
    
    return score, comments

def analyze_enhanced_score(dif_values, macd_values, volume_data=None, volume_ma5_data=None):
    """
    扩展版技术指标分析并计算得分（完善成交量分析）
    """
    # 首先调用基础MACD分析
    score, comments = analyze_macd_score(dif_values, macd_values)
    
    # 成交量分析
    if volume_data is not None and volume_ma5_data is not None:
        if len(volume_data) >= 1 and len(volume_ma5_data) >= 1:
            latest_volume = volume_data[0]
            latest_mavol5 = volume_ma5_data[0]
            
            # 8. 放量分析（超过5日均量2倍）
            if latest_volume > latest_mavol5 * 2:
                score += 1
                comments.append("放量超过MAVOL5两倍以上")
        
        # 9. DIF持续在0轴上方（最近3个周期）
        if len(dif_values) >= 3:
            dif_above_zero_continuous = all(dif > 0 for dif in dif_values[:3])
            if dif_above_zero_continuous:
                score += 1
                comments.append("DIF持续在0轴上方，加1分")
        
        # 10. 成交量均线上涨趋势（最近3个周期）
        if len(volume_ma5_data) >= 3:
            mavol5_rising_count = 0
            for i in range(min(2, len(volume_ma5_data)-1)):
                if volume_ma5_data[i] > volume_ma5_data[i+1]:
                    mavol5_rising_count += 1
            
            if mavol5_rising_count >= 2:
                score += 1
                comments.append("MAVOL5连续上涨")
    
    return score, comments

def analyze_comprehensive_indicators(dif_values, dea_values, macd_values, volume_data, volume_ma5_data, 
                                   close_prices, ma5_values, ma10_values, rsi6_values):
    """
    全面的技术指标分析，包含28个技术分析信号（修正版本）
    
    参数:
        dif_values: DIF值列表 (最新的在前)
        dea_values: DEA值列表 (最新的在前)
        macd_values: MACD值列表 (最新的在前)
        volume_data: 成交量数据列表 (最新的在前)
        volume_ma5_data: 5日成交量均线数据列表 (最新的在前)
        close_prices: 收盘价列表 (最新的在前)
        ma5_values: MA5值列表 (最新的在前)
        ma10_values: MA10值列表 (最新的在前)
        rsi6_values: RSI6值列表 (最新的在前)
    
    返回:
        score: 综合得分
        categorized_comments: 分类后的详细分析评论列表
    """
    score = 0
    categorized_comments = {
        'MACD': [], 'MA': [], 'RSI': [], '成交量': []
    }
    
    # 数据长度检查
    data_lengths = [len(dif_values), len(macd_values), len(volume_data), 
                   len(volume_ma5_data), len(close_prices), len(ma5_values), 
                   len(ma10_values), len(rsi6_values)]
    min_length = min(data_lengths)
    
    if min_length < 2:
        categorized_comments['MACD'].append("数据不足，无法进行全面技术分析")
        return 0, categorized_comments
    
    # === MACD类指标分析（修正版本）===
    
    # 1. DIF值拐头向上 - DIF经历先降后升过程且持续上涨
    if len(dif_values) >= 5:
        found_turn_up = False
        # 寻找谷底拐点（V型反转）
        for i in range(1, min(4, len(dif_values) - 1)):
            if (dif_values[i] < dif_values[i+1] and dif_values[i] < dif_values[i-1]):
                # 确认拐点后的持续上涨
                if all(dif_values[j] > dif_values[j+1] for j in range(i)):
                    found_turn_up = True
                    break
        
        if found_turn_up:
            # score += 1 # 移除计分
            categorized_comments['MACD'].append("DIF拐头向上")
    
    # 2. DIF值持续上涨 - 最近5个周期DIF连续上升
    if len(dif_values) >= 5:
        dif_continuous_rising = all(dif_values[i] > dif_values[i+1] for i in range(4))
        if dif_continuous_rising:
            # score += 1 # 移除计分
            categorized_comments['MACD'].append("DIF持续上涨")
    
    # 3. DIF值拐头向下 - DIF经历先升后降过程且持续下跌
    if len(dif_values) >= 5:
        found_turn_down = False
        # 寻找峰顶拐点（倒V型反转）
        for i in range(1, min(4, len(dif_values) - 1)):
            if (dif_values[i] > dif_values[i+1] and dif_values[i] > dif_values[i-1]):
                # 确认拐点后的持续下跌
                if all(dif_values[j] < dif_values[j+1] for j in range(i)):
                    found_turn_down = True
                    break
        
        if found_turn_down:
            # score -= 1 # 移除计分
            categorized_comments['MACD'].append("DIF拐头向下")
    
    # 4. DIF持续下降 - 最近5个周期DIF连续下降
    if len(dif_values) >= 5:
        dif_continuous_falling = all(dif_values[i] < dif_values[i+1] for i in range(4))
        if dif_continuous_falling:
            # score -= 1 # 移除计分
            categorized_comments['MACD'].append("DIF持续下降")
    
    # 5. DIF上穿0轴或持续在0轴上方
    if len(dif_values) >= 5:
        # 检查DIF上穿0轴（最近5个周期内）
        dif_crossed_up = False
        for i in range(4):
            if dif_values[i] > 0 and dif_values[i+1] <= 0:
                # 确认上穿后持续在0轴上方
                if all(dif_values[j] > 0 for j in range(i+1)):
                    dif_crossed_up = True
                    break
        
        # 检查DIF持续在0轴上方
        dif_above_zero = all(dif > 0 for dif in dif_values[:5])
        
        if dif_crossed_up:
            score += 1  # 恢复计分
            categorized_comments['MACD'].append("DIF上穿0轴且持续在上方")
        elif dif_above_zero:
            score += 1  # 恢复计分
            categorized_comments['MACD'].append("DIF持续在0轴上方")
    
    # 6. MACD红柱持续变长 - 最近4个周期MACD>0且连续增大
    if len(macd_values) >= 4:
        macd_red_growing = (all(macd_values[i] > 0 for i in range(4)) and 
                           all(macd_values[i] > macd_values[i+1] for i in range(3)))
        if macd_red_growing:
            # score += 1 # 移除计分
            categorized_comments['MACD'].append("MACD红柱持续变长")
    
    # 7. MACD红柱开始缩短 - MACD>0但开始变小
    if len(macd_values) >= 2:
        if (macd_values[0] > 0 and macd_values[1] > 0 and 
            macd_values[0] < macd_values[1]):
            # score -= 1 # 移除计分
            categorized_comments['MACD'].append("MACD红柱开始缩短")
    
    # 8 & 9. MACD金叉/死叉 - 以最近出现的信号为准
    if len(dif_values) >= 5 and len(dea_values) >= 5:
        last_golden_cross_idx = -1
        last_death_cross_idx = -1

        # 寻找最近的金叉
        for i in range(4):
            if (dif_values[i] > dea_values[i] and dif_values[i+1] <= dea_values[i+1]):
                if all(dif_values[j] > dea_values[j] for j in range(i + 1)):
                    last_golden_cross_idx = i
                    break
        
        # 寻找最近的死叉
        for i in range(4):
            if (dif_values[i] < dea_values[i] and dif_values[i+1] >= dea_values[i+1]):
                if all(dif_values[j] < dea_values[j] for j in range(i + 1)):
                    last_death_cross_idx = i
                    break

        # 比较哪个信号更近
        if last_golden_cross_idx != -1 and last_death_cross_idx != -1:
            # 如果金叉和死叉都出现了，以更近的（索引更小）为准
            if last_golden_cross_idx < last_death_cross_idx:
                # score += 1 # 移除计分
                categorized_comments['MACD'].append("MACD金叉")
            else:
                # score -= 2 # 移除计分
                categorized_comments['MACD'].append("MACD死叉")
        elif last_golden_cross_idx != -1:
            # 只出现了金叉
            # score += 1 # 移除计分
            categorized_comments['MACD'].append("MACD金叉")
        elif last_death_cross_idx != -1:
            # 只出现了死叉
            # score -= 2 # 移除计分
            categorized_comments['MACD'].append("MACD死叉")
    
    # === 移动平均线指标分析（检查最近5个周期）===
    
    # 10. MA5拐头向上 - MA5从下跌转为上涨，且转为上涨后持续上涨，不再下跌
    if len(ma5_values) >= 5:
        found_ma5_turn_up = False
        for i in range(len(ma5_values) - 2):  # 避免越界
            if i + 2 < len(ma5_values) and ma5_values[i+1] < ma5_values[i] and ma5_values[i+1] < ma5_values[i+2]:
                # 找到拐点，检查拐点后是否持续上涨
                turn_point = i + 1
                continued_rising = True
                for j in range(turn_point):
                    if ma5_values[j] <= ma5_values[j+1]:
                        continued_rising = False
                        break
                if continued_rising:
                    found_ma5_turn_up = True
                    break
        
        if found_ma5_turn_up:
            # score += 1 # 移除计分
            categorized_comments['MA'].append("MA5拐头向上")
    
    # 11. MA5上穿MA10 - ma5上穿ma10，且不再穿回来
    if len(ma5_values) >= 5 and len(ma10_values) >= 5:
        found_ma_golden_cross = False
        for i in range(4):  # 检查最近5个周期
            if ma5_values[i] > ma10_values[i] and ma5_values[i+1] <= ma10_values[i+1]:
                # 找到上穿点，检查之后是否持续在MA10上方
                stayed_above = all(ma5_values[j] > ma10_values[j] for j in range(i+1))
                if stayed_above:
                    found_ma_golden_cross = True
                    break
        
        if found_ma_golden_cross:
            # score += 2 # 移除计分
            categorized_comments['MA'].append("MA5上穿MA10")
    
    # 12. MA5下穿MA10 - ma5下穿ma10，且不再穿回去
    if len(ma5_values) >= 5 and len(ma10_values) >= 5:
        found_ma_death_cross = False
        for i in range(4):  # 检查最近5个周期
            if ma5_values[i] < ma10_values[i] and ma5_values[i+1] >= ma10_values[i+1]:
                # 找到下穿点，检查之后是否持续在MA10下方
                stayed_below = all(ma5_values[j] < ma10_values[j] for j in range(i+1))
                if stayed_below:
                    found_ma_death_cross = True
                    break
        
        if found_ma_death_cross:
            # score -= 2 # 移除计分
            categorized_comments['MA'].append("MA5下穿MA10")
    
    # 13. 股价持续运行在MA5之上 - 连续5根K线收盘价都在MA5之上
    if len(close_prices) >= 5 and len(ma5_values) >= 5:
        price_above_ma5 = all(close_prices[i] > ma5_values[i] for i in range(5))
        if price_above_ma5:
            # score += 1 # 移除计分
            categorized_comments['MA'].append("股价持续运行在MA5之上")
    
    # === RSI指标分析（检查最近5个周期）===
    
    # 14. RSI6上穿50 - RSI6从50以下上穿到50以上，且不再跌回50以下
    if len(rsi6_values) >= 5:
        found_rsi_cross_up = False
        for i in range(4):  # 检查最近5个周期
            if rsi6_values[i] > 50 and rsi6_values[i+1] <= 50:
                # 找到上穿点，检查之后是否持续在50上方
                stayed_above = all(rsi6_values[j] > 50 for j in range(i+1))
                if stayed_above:
                    found_rsi_cross_up = True
                    break
        
        if found_rsi_cross_up:
            # score += 1 # 移除计分
            categorized_comments['RSI'].append("RSI6上穿50")
    
    # 15. RSI6下穿50 - RSI6从50以上下穿到50以下，且不再穿回到50以上
    if len(rsi6_values) >= 5:
        found_rsi_cross_down = False
        for i in range(4):  # 检查最近5个周期
            if rsi6_values[i] < 50 and rsi6_values[i+1] >= 50:
                # 找到下穿点，检查之后是否持续在50下方
                stayed_below = all(rsi6_values[j] < 50 for j in range(i+1))
                if stayed_below:
                    found_rsi_cross_down = True
                    break
        
        if found_rsi_cross_down:
            # score -= 1 # 移除计分
            categorized_comments['RSI'].append("RSI6下穿50")
    
    # 16. RSI6始终运行在50以上 - 所有周期RSI6都在50以上
    if len(rsi6_values) >= 5:
        rsi6_above_50 = all(rsi > 50 for rsi in rsi6_values[:5])
        if rsi6_above_50:
            # score += 1 # 移除计分
            categorized_comments['RSI'].append("RSI6始终运行在50以上")
    
    # === 成交量指标分析（判断倒数第6个周期开始，倒数第2个周期结束，共5根k线范围内）===
    
    # 确保有足够的数据进行成交量分析
    if len(volume_data) >= 6 and len(volume_ma5_data) >= 6:        
        # 17. 放量超过MAVOL5两倍以上 - 检查范围内是否有成交量超过5日均量的2倍
        found_heavy_volume = False
        for i in range(1, 6):  # 倒数第6到倒数第2个周期
            if volume_data[i] > volume_ma5_data[i] * 2:
                found_heavy_volume = True
                break
        
        if found_heavy_volume:
            # score += 1 # 移除计分
            categorized_comments['成交量'].append("放量超过MAVOL5两倍以上")
        
        # 18. 4根及以上K线的MAVOL5上涨 - 检查范围内至少有4个周期的5日均量比前值上涨
        mavol5_rising_count = 0
        for i in range(1, 5):  # 索引1,2,3,4（每个都与前一个比较）
            if volume_ma5_data[i] > volume_ma5_data[i+1]:
                mavol5_rising_count += 1
        
        if mavol5_rising_count >= 4:
            # score += 1 # 移除计分
            categorized_comments['成交量'].append("4根及以上K线的MAVOL5上涨")
        
        # 19. 3根及以上K线的MAVOL5下跌 - 检查范围内至少有3个周期的5日均量比前值下跌
        mavol5_falling_count = 0
        for i in range(1, 5):  # 索引1,2,3,4（每个都与前一个比较）
            if volume_ma5_data[i] < volume_ma5_data[i+1]:
                mavol5_falling_count += 1
        
        if mavol5_falling_count >= 3:
            # score -= 1 # 移除计分
            categorized_comments['成交量'].append("3根及以上K线的MAVOL5下跌")
        
        # 20. 4根及以上K线的VOL大于MAVOL5 - 检查范围内4个或以上周期成交量都大于5日均量
        vol_above_mavol5_count = sum(1 for i in range(1, 6) 
                                   if volume_data[i] > volume_ma5_data[i])
        
        if vol_above_mavol5_count >= 4:
            # score += 1 # 移除计分
            categorized_comments['成交量'].append("4根及以上K线的VOL大于MAVOL5")
        
        # 21. 4根及以上K线的VOL小于MAVOL5 - 检查范围内4个或以上周期成交量都小于5日均量
        vol_below_mavol5_count = sum(1 for i in range(1, 6) 
                                   if volume_data[i] < volume_ma5_data[i])
        
        if vol_below_mavol5_count >= 4:
            # score -= 1 # 移除计分
            categorized_comments['成交量'].append("4根及以上K线的VOL小于MAVOL5")
    
    # === 价量关系分析 ===
    
    # 注意：背离分析已迁移到新的analyze_divergence函数中，此处不再进行背离分析
    
    return score, categorized_comments

def analyze_stock_technical_score(dif_values, dea_values, macd_values, volume_data, 
                                 volume_ma5_data, close_prices, ma5_values, ma10_values, 
                                 rsi6_values, kdj_k_values=None, kdj_d_values=None):
    """
    统一的股票技术分析评分函数（28个指标完整版本）
    
    参数:
        dif_values: DIF值列表 (最新的在前)
        dea_values: DEA值列表 (最新的在前)  
        macd_values: MACD值列表 (最新的在前)
        volume_data: 成交量数据列表 (最新的在前)
        volume_ma5_data: 5日成交量均线数据列表 (最新的在前)
        close_prices: 收盘价列表 (最新的在前)
        ma5_values: MA5值列表 (最新的在前)
        ma10_values: MA10值列表 (最新的在前)
        rsi6_values: RSI6值列表 (最新的在前)
        kdj_k_values: KDJ K值列表 (最新的在前, 可选)
        kdj_d_values: KDJ D值列表 (最新的在前, 可选)
    
    返回:
        total_score: 总得分
        detailed_comments: 详细分析评论列表
        summary: 分析摘要
    """
    
    # 使用已修正的综合指标分析函数
    total_score, detailed_comments = analyze_comprehensive_indicators(
        dif_values, dea_values, macd_values, volume_data, volume_ma5_data,
        close_prices, ma5_values, ma10_values, rsi6_values
    )
    
    # 分析摘要
    if total_score >= 15:
        strength = "强势看涨"
    elif total_score >= 10:
        strength = "偏强势"
    elif total_score >= 5:
        strength = "中性偏强"
    elif total_score >= 0:
        strength = "中性"
    elif total_score >= -5:
        strength = "中性偏弱"
    elif total_score >= -10:
        strength = "偏弱势"
    else:
        strength = "弱势看跌"
    
    summary = {
        'total_score': total_score,
        'strength_level': strength,
        'indicator_count': sum(len(v) for v in detailed_comments.values()),
        'positive_signals': len([c for c in detailed_comments.get('MACD', []) if '加' in c]),
        'negative_signals': len([c for c in detailed_comments.get('MACD', []) if '减' in c])
    }
    
    return total_score, detailed_comments, summary

def format_technical_analysis_output(stock_code, total_score, detailed_comments, summary, 
                                   current_price=None, change_pct=None):
    """
    格式化技术分析输出报告
    
    参数:
        stock_code: 股票代码
        total_score: 总得分
        detailed_comments: 详细分析评论列表
        summary: 分析摘要
        current_price: 当前价格 (可选)
        change_pct: 涨跌幅 (可选)
    
    返回:
        formatted_output: 格式化的分析报告字符串
    """
    
    # 构建报告头部
    output_lines = []
    output_lines.append("=" * 60)
    output_lines.append(f"技术指标分析报告 - {stock_code}")
    output_lines.append("=" * 60)
    
    if current_price and change_pct:
        output_lines.append(f"当前价格: {current_price:.2f}  涨跌幅: {change_pct:+.2f}%")
        output_lines.append("-" * 60)
    
    # 分析摘要
    output_lines.append("【分析摘要】")
    output_lines.append(f"综合评分: {total_score} 分")
    output_lines.append(f"强弱判断: {summary['strength_level']}")
    output_lines.append(f"信号统计: 看涨信号 {summary['positive_signals']} 个, 看跌信号 {summary['negative_signals']} 个")
    output_lines.append("")
    
    # 详细分析
    output_lines.append("【详细分析】")
    if detailed_comments:
        for i, comment in enumerate(detailed_comments, 1):
            output_lines.append(f"{i:2d}. {comment}")
    else:
        output_lines.append("暂无详细分析信息")
    
    output_lines.append("")
    output_lines.append("=" * 60)
    
    # 投资建议
    output_lines.append("【投资建议】")
    if total_score >= 10:
        suggestion = "技术面偏强，可考虑关注买入机会，注意控制仓位"
    elif total_score >= 5:
        suggestion = "技术面中性偏强，可适量关注，等待更明确信号"
    elif total_score >= -5:
        suggestion = "技术面中性，建议观望，等待明确方向"
    else:
        suggestion = "技术面偏弱，建议谨慎，避免盲目进场"
    
    output_lines.append(suggestion)
    output_lines.append("=" * 60)
    
    return "\n".join(output_lines)

# 注意：原来的find_macd_regions和analyze_macd_divergence函数已被新的analyze_divergence函数替代

# 添加miniqmt支持
try:
    from xtquant.xttrader import XtQuantTrader
    from xtquant.xttype import StockAccount
    from xtquant import xtdata
except ImportError:
    print("警告: 未安装xtquant，miniqmt功能将不可用")

# 添加全局变量用于miniqmt连接
MINIQMT_CONNECTED = False
XT_TRADER = None

# 添加miniqmt连接函数
def connect_miniqmt():
    """连接miniqmt - 使用更简单的直接连接方式"""
    global MINIQMT_CONNECTED, XT_TRADER
    try:
        print("连接QMT...")
        # 直接使用xtdata.connect()方法
        connect_result = xtdata.connect()
        print(f"连接结果: {connect_result}")
        
        if connect_result:
            print("miniqmt连接成功")
            MINIQMT_CONNECTED = True
            return True
        else:
            print("miniqmt连接失败，请检查QMT客户端是否运行")
            return False
    except Exception as e:
        print(f"miniqmt连接异常: {e}")
        import traceback
        traceback.print_exc()
        return False

# 添加miniqmt数据下载函数
def download_miniqmt_data(stock_code, period, start_time='', end_time=''):
    """下载miniqmt历史数据"""
    global MINIQMT_CONNECTED
    if not MINIQMT_CONNECTED:
        if not connect_miniqmt():
            print("miniqmt未连接，无法下载数据")
            return False
    
    try:
        # 转换时间格式
        if start_time:
            start_time = start_time.replace('-', '')
        if end_time:
            end_time = end_time.replace('-', '')
        
        xtdata.download_history_data(
            stock_code=stock_code,
            period=period,
            start_time=start_time,
            end_time=end_time  # 添加结束时间参数
        )
        # 增加等待时间，确保数据下载完成
        #time.sleep(2)
        return True
    except Exception as e:
        print(f"下载数据异常: {e}")
        return False

# 添加统一下载行情数据的函数
def download_all_data_for_stock(stock_code):
    """
    统一下载指定股票的各周期行情数据
    
    参数:
        stock_code: 股票代码，如"600000"
    
    下载:
        1. 日线（近半年到今天）
        2. 周线（近半年到今天）
        3. 5分钟线（近10个交易日）
    """
    # 格式化股票代码为带交易所后缀的格式
    if stock_code.lower().startswith(('sh', 'sz')):
        code_only = stock_code[2:]
    else:
        code_only = stock_code
        
    # 添加交易所后缀
    if code_only.startswith('6'):
        formatted_code = f"{code_only}.SH"  # 上海证券交易所
    elif code_only.startswith(('0', '3')):
        formatted_code = f"{code_only}.SZ"  # 深圳证券交易所
    elif code_only.startswith(('880', '881')):
        formatted_code = f"{code_only}.SH"  # 板块指数（上海）
    elif code_only.startswith('399'):
        formatted_code = f"{code_only}.SZ"  # 深圳指数
    else:
        print(f"无法识别的股票代码格式: {stock_code}")
        return False
    
    # 计算时间范围
    today = datetime.now()
    
    # 1. 下载日线（近半年到今天）
    half_year_ago = (today - timedelta(days=180)).strftime('%Y%m%d')
    today_str = today.strftime('%Y%m%d')
    if not download_miniqmt_data(formatted_code, '1d', half_year_ago, today_str):
        print(f"日线数据下载失败")
    
    # 2. 下载周线（近半年到今天）
    if not download_miniqmt_data(formatted_code, '1w', half_year_ago, today_str):
        print(f"周线数据下载失败")
    
    # 3. 下载5分钟线（近10个交易日）
    ten_days_ago = (today - timedelta(days=14)).strftime('%Y%m%d')  # 使用14天确保有10个交易日
    if not download_miniqmt_data(formatted_code, '5m', ten_days_ago, today_str):
        print(f"5分钟线数据下载失败")
    
    # 额外下载30分钟线（用于MACD计算）
    if not download_miniqmt_data(formatted_code, '30m', ten_days_ago, today_str):
        print(f"30分钟线数据下载失败")
    
    return True

# 修改get_miniqmt_data函数，避免重复下载日线数据
def get_miniqmt_data(stock_code, period='1d', count=500, dividend_type='front'):
    """从miniqmt获取K线数据（添加30分钟数据下载）"""
    global MINIQMT_CONNECTED
    if not MINIQMT_CONNECTED:
        print("miniqmt未连接，尝试连接...")
        if connect_miniqmt():
            print("miniqmt连接成功")
        else:
            print("miniqmt连接失败，无法获取数据")
            return pd.DataFrame()
    
    try:
        # 格式化股票代码为带交易所后缀的格式
        if stock_code.lower().startswith(('sh', 'sz')):
            code_only = stock_code[2:]
        else:
            code_only = stock_code
            
        # 添加交易所后缀
        if code_only.startswith('6'):
            formatted_code = f"{code_only}.SH"  # 上海证券交易所
        elif code_only.startswith(('0', '3')):
            formatted_code = f"{code_only}.SZ"  # 深圳证券交易所
        elif code_only.startswith(('880', '881')):
            formatted_code = f"{code_only}.SH"  # 板块指数（上海）
        elif code_only.startswith('399'):
            formatted_code = f"{code_only}.SZ"  # 深圳指数
        else:
            print(f"无法识别的股票代码格式: {stock_code}")
            return pd.DataFrame()
        
        # 对于30分钟数据，先下载历史数据确保有足够数据
        if period == '30m':
            # 计算开始日期（确保有足够历史数据）
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
            download_miniqmt_data(formatted_code, '30m', start_date, end_date)
            #time.sleep(2)  # 增加等待时间确保数据写入缓存
        
        # 直接获取K线数据
        kline_data = xtdata.get_market_data(
            field_list=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_list=[formatted_code],
            period=period,
            count=count,
            dividend_type=dividend_type,
            fill_data=True
        )
        
        # 检查是否成功获取数据
        if kline_data is None:
            print(f"无法获取{formatted_code}的数据")
            return pd.DataFrame()
        
        # 关键修改：统一处理时间数据格式
        # 获取时间数据并转换为标准格式
        time_data = kline_data.get('time')
        
        # 检查时间数据是否有效
        if time_data is None:
            print(f"未获取到{formatted_code}的时间数据")
            return pd.DataFrame()
        
        # 统一处理时间数据格式
        if isinstance(time_data, pd.DataFrame):
            # DataFrame格式 - 提取股票代码对应的时间列
            if formatted_code in time_data.columns:
                time_series = time_data[formatted_code]
            elif formatted_code in time_data.index:
                time_series = time_data.loc[formatted_code]
            else:
                print(f"无法在时间数据中找到股票代码 {formatted_code}")
                return pd.DataFrame()
        elif isinstance(time_data, pd.Series):
            # Series格式 - 直接使用
            time_series = time_data
        else:
            print(f"无法识别的时间数据类型: {type(time_data)}")
            return pd.DataFrame()
        
        # === 修复时间戳解析 ===
        # 打印原始时间戳用于调试
        # 移除调试输出
        
        try:
            # 尝试秒级解析 (QMT可能返回秒级时间戳)
            utc_dates = pd.to_datetime(time_series, unit='s', utc=True)
        except:
            # 尝试毫秒级解析 (备用方案)
            utc_dates = pd.to_datetime(time_series, unit='ms', utc=True)
        
        # 转换为本地时间并移除时区信息
        dates = utc_dates.dt.tz_convert('Asia/Shanghai').dt.tz_localize(None)
        # === 时间戳修复结束 ===
        
        # 创建结果DataFrame
        result_df = pd.DataFrame({
            '日期': dates
        })
        
        # 添加数据完整性检查
        required_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
        for field in required_columns:
            if field in kline_data:
                field_data = kline_data[field]
                
                # 统一处理字段数据格式
                if isinstance(field_data, pd.DataFrame):
                    # DataFrame格式 - 提取股票代码对应的列
                    if formatted_code in field_data.columns:
                        result_df[field] = field_data[formatted_code].values
                    elif formatted_code in field_data.index:
                        result_df[field] = field_data.loc[formatted_code].values
                    else:
                        print(f"无法在{field}数据中找到股票代码 {formatted_code}")
                        result_df[field] = 0.0
                elif isinstance(field_data, pd.Series):
                    # Series格式 - 直接使用
                    result_df[field] = field_data.values
                else:
                    print(f"无法识别的{field}数据类型: {type(field_data)}")
                    result_df[field] = 0.0
            else:
                print(f"警告: {field}字段不存在")
                result_df[field] = 0.0
        
        # 统一列名
        result_df.rename(columns={
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量',
            'amount': '成交额',
            'date': '日期',
            'time': '日期'  # 确保time列也被重命名为日期
        }, inplace=True)
        
        # 确保列名已被统一转换为中文
        for en, cn in [('open', '开盘'), ('high', '最高'), ('low', '最低'), 
                       ('close', '收盘'), ('volume', '成交量'), ('amount', '成交额')]:
            if en in result_df.columns and cn not in result_df.columns:
                result_df.rename(columns={en: cn}, inplace=True)
        
        # 调试输出当前列名
        # 移除调试输出
        
        # 检查结果DataFrame是否为空
        if result_df.empty:
            print(f"结果DataFrame为空")
            return pd.DataFrame()
            
        # 移除调试信息
        
        # 按日期升序排列 (旧->新) 确保最新数据在最后
        result_df = result_df.sort_values('日期', ascending=True).reset_index(drop=True)
        
        return result_df
        
    except Exception as e:
        print(f"从miniqmt获取数据出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame([{"error": f"数据获取失败: {str(e)}"}])

def get_daily_data(stock_code, data_source='miniqmt'):
    """
    获取日线数据 - 只使用miniqmt数据源
    """
    df = get_miniqmt_data(stock_code, period='1d')
    
    # 检查是否包含错误信息
    if not df.empty and 'error' in df.columns:
        print(f"获取数据失败: {df.iloc[0]['error']}")
        return df
    
    if df is not None:
        # 统一列名
        df.rename(columns={'日期': '日期'}, inplace=True)
    return df

def get_block_data(block_code):
    """获取板块日线数据 - 从通达信本地文件读取"""
    return get_daily_data(block_code)

# 更新日K线聚合函数，作为备用方案
def calculate_weekly_data(daily_data):
    """根据日线数据计算周线数据（精确处理每周五的日期）"""
    if daily_data.empty:
        return pd.DataFrame()
    
    # 确保日期列被正确处理
    date_col = None
    if '日期' in daily_data.columns:
        date_col = '日期'
        daily_data_copy = daily_data.copy()
    else:
        daily_data_copy = daily_data.reset_index()
        if 'index' in daily_data_copy.columns:
            date_col = 'index'
        elif 'date' in daily_data_copy.columns:
            date_col = 'date'
    
    if date_col is None:
        raise ValueError("无法找到日期列")
    
    # 记住原始排序方向
    original_order_descending = daily_data_copy[date_col].is_monotonic_decreasing
    
    # 转换为datetime类型
    daily_data_copy[date_col] = pd.to_datetime(daily_data_copy[date_col])
    
    # 按日期升序排序（旧->新）
    daily_data_copy = daily_data_copy.sort_values(date_col)
    
    # 添加ISO年份和周数列
    try:
        # pandas 1.1.0及以上版本的写法
        daily_data_copy['year_week'] = daily_data_copy[date_col].dt.isocalendar().year.astype(str) + '-' + \
                                      daily_data_copy[date_col].dt.isocalendar().week.astype(str)
    except:
        # 兼容旧版本pandas的替代方法
        daily_data_copy['year_week'] = daily_data_copy[date_col].apply(
            lambda x: f"{x.isocalendar()[0]}-{x.isocalendar()[1]}")
    
    # 按周分组聚合
    grouped = daily_data_copy.groupby('year_week')
    
    result_data = []
    for _, group in grouped:
        # 先排序确保日期有序
        group = group.sort_values(date_col)
        
        # 查找组内的周五日期
        friday = None
        for date_val in group[date_col]:
            if date_val.weekday() == 4:  # 周五
                friday = date_val
                break
        
        # 如果没有周五，使用当周最后一个交易日
        if friday is None:
            friday = group[date_col].max()
        
        # 获取当周数据
        first_open = group['开盘'].iloc[0]
        highest = group['最高'].max()
        lowest = group['最低'].min()
        last_close = group['收盘'].iloc[-1]
        volume_sum = group['成交量'].sum()
        amount_sum = group['成交额'].sum() if '成交额' in group.columns else 0
        
        result_data.append({
            '日期': friday,
            '开盘': first_open,
            '最高': highest,
            '最低': lowest,
            '收盘': last_close,
            '成交量': volume_sum,
            '成交额': amount_sum
        })
    
    # 创建周线DataFrame
    weekly_df = pd.DataFrame(result_data)
    
    # 根据原始排序方式排序
    if original_order_descending:
        weekly_df = weekly_df.sort_values('日期', ascending=False).reset_index(drop=True)
    else:
        weekly_df = weekly_df.sort_values('日期', ascending=True).reset_index(drop=True)
    
    # 统一列名
    weekly_df.rename(columns={
        'open': '开盘',
        'high': '最高',
        'low': '最低',
        'close': '收盘',
        'volume': '成交量',
        'amount': '成交额'
    }, inplace=True)
    
    return weekly_df

def get_weekly_data(stock_code, count=500):
    """
    获取周线数据 - 只使用miniqmt
    """
    # 直接获取周K线数据
    try:
        # 格式化股票代码
        if stock_code.lower().startswith(('sh', 'sz')):
            code_only = stock_code[2:]
        else:
            code_only = stock_code
            
        if code_only.startswith('6') or code_only.startswith(('880', '881')):
            formatted_code = f"{code_only}.SH"
        elif code_only.startswith(('0', '3', '399')):
            formatted_code = f"{code_only}.SZ"
        else:
            print(f"无法识别的股票代码格式: {stock_code}")
            return pd.DataFrame()
        
        # 直接获取周K线
        kline_data = xtdata.get_market_data(
            field_list=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_list=[formatted_code],
            period='1w',  # 关键：直接指定周线周期
            count=count,
            dividend_type='front',  # 关键参数：指定前复权
            fill_data=True
        )
        
        # 检查是否成功获取数据
        if kline_data is None or 'time' not in kline_data:
            print(f"无法获取{formatted_code}的周线数据")
            return pd.DataFrame()
        
        # 创建结果DataFrame
        result_df = pd.DataFrame()
        dates = pd.to_datetime(kline_data.get('time').loc[formatted_code], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai').dt.tz_localize(None)
        
        result_df['日期'] = dates
        
        # 添加其他字段
        for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
            if field in kline_data and isinstance(kline_data[field], pd.DataFrame):
                if formatted_code in kline_data[field].index:
                    result_df[field] = kline_data[field].loc[formatted_code].values
                else:
                    print(f"警告: {field}数据中没有{formatted_code}")
                    result_df[field] = 0
            else:
                print(f"警告: {field}字段不存在或格式不正确")
                result_df[field] = 0
        
        # 移除时区信息(如果有)
        if hasattr(result_df['日期'].dt, 'tz') and result_df['日期'].dt.tz is not None:
            result_df['日期'] = result_df['日期'].dt.tz_convert(None)
        
        # 按日期升序排列 (旧->新) ！！！关键修改：改变排序方向
        result_df = result_df.sort_values('日期', ascending=True).reset_index(drop=True)
        
        # 统一列名
        result_df.rename(columns={
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量',
            'amount': '成交额'
        }, inplace=True)
        
        return result_df
    except Exception as e:
        print(f"获取周K线数据出错: {e}")
        return pd.DataFrame()

def get_min_data(stock_code, period='30', data_source='miniqmt'):
    """获取分钟级数据 - 只使用miniqmt"""
    # 映射周期参数
    period_map = {
        '1': '1m',
        '5': '5m',
        '15': '15m',
        '30': '30m',
        '60': '60m'
    }
    qmt_period = period_map.get(period, '5m')
    df = get_miniqmt_data(stock_code, period=qmt_period, count=1000)
    # 统一列名
    df.rename(columns={
        'time': '日期',
        'open': '开盘',
        'high': '最高',
        'low': '最低',
        'close': '收盘',
        'volume': '成交量',
        'amount': '成交额'
    }, inplace=True)
    return df

# 添加获取30分钟K线数据函数，返回最新5个周期
def get_30min_data(stock_code):
    """
    获取30分钟K线数据，直接打印原始数据
    """
    # 增加数据量确保获取到足够数据
    df = get_miniqmt_data(stock_code, period='30m', count=1000)
    
    # 返回空DataFrame，因为用户只需要打印原始数据
    return pd.DataFrame()

def calculate_macd(data, fastperiod=12, slowperiod=26, signalperiod=9, is_weekly=False):
    """计算MACD指标（添加is_weekly参数支持）"""
    close_col = get_column_name(data, 'close', '收盘')
    if close_col is None:
        raise ValueError(f"无法找到收盘价列，可用列: {data.columns.tolist()}")
        
    close = data[close_col]
    
    # 普通日线计算
    macd_dif, macd_dea, macd_hist = calc_macd(close, 
                                             fastperiod=fastperiod, 
                                             slowperiod=slowperiod, 
                                             signalperiod=signalperiod)
    return macd_dif, macd_dea, macd_hist

def calculate_kdj(data, fastk_period=9, slowk_period=3, slowd_period=3):
    """计算KDJ指标"""
    # 获取列名
    high_col = get_column_name(data, 'high', '最高')
    low_col = get_column_name(data, 'low', '最低')
    close_col = get_column_name(data, 'close', '收盘')
    
    if high_col is None or low_col is None or close_col is None:
        raise ValueError(f"无法找到必要的列，可用列: {data.columns.tolist()}")
    
    high = data[high_col]
    low = data[low_col]
    close = data[close_col]
    
    k, d, j = calc_kdj(high, low, close, 
                       fastk_period=fastk_period,
                       slowk_period=slowk_period, 
                       slowd_period=slowd_period)
    return k, d, j

def calculate_bias(data, timeperiod=6):
    """计算BIAS指标"""
    close_col = get_column_name(data, 'close', '收盘')
    if close_col is None:
        raise ValueError(f"无法找到收盘价列，可用列: {data.columns.tolist()}")
        
    close = data[close_col]
    bias = calc_bias(close, timeperiod=timeperiod)
    return bias

def calculate_bollinger(data, timeperiod=20, nbdevup=2, nbdevdn=2):
    """
    计算布林带指标
    
    参数:
        data: 数据DataFrame
        timeperiod: 移动平均周期
        nbdevup: 上轨标准差倍数
        nbdevdn: 下轨标准差倍数
    """
    close_col = get_column_name(data, 'close', '收盘')
    if close_col is None:
        raise ValueError(f"无法找到收盘价列，可用列: {data.columns.tolist()}")
        
    close = data[close_col]
    
    upper, middle, lower = calc_bollinger(close, 
                                            timeperiod=timeperiod,
                                            nbdevup=nbdevup, 
                                            nbdevdn=nbdevdn)
    return upper, middle, lower

def calculate_ma(data, periods=[5, 10, 60]):
    """计算移动平均线"""
    close_col = get_column_name(data, 'close', '收盘')
    if close_col is None:
        raise ValueError(f"无法找到收盘价列，可用列: {data.columns.tolist()}")
        
    close = data[close_col]
    results = {}
    for period in periods:
        results[f'MA{period}'] = calc_ma(close, period)
    return results

def calculate_rsi(data, periods=[6, 12, 24]):
    """计算RSI指标"""
    close_col = get_column_name(data, 'close', '收盘')
    if close_col is None:
        raise ValueError(f"无法找到收盘价列，可用列: {data.columns.tolist()}")
        
    close = data[close_col]
    results = {}
    for period in periods:
        results[f'RSI{period}'] = calc_rsi(close, period)
    return results

def calculate_volume_ma(data, periods=[5, 10]):
    """计算成交量移动平均线"""
    volume_col = get_column_name(data, 'volume', '成交量')
    if volume_col is None:
        raise ValueError(f"无法找到成交量列，可用列: {data.columns.tolist()}")
        
    volume = data[volume_col]
    results = {}
    for period in periods:
        results[f'VMA{period}'] = calc_ma(volume, period)
    return results

def get_stock_news(stock_code=None):
    """
    获取东方财富股票新闻
    
    参数:
        stock_code: 股票代码，如"600519"，默认为None返回全市场新闻
                    注意：此函数不支持板块代码
    
    返回:
        pandas.DataFrame: 新闻数据
    """
    try:
        if ak is not None:
            # 优先使用akshare获取实际新闻
            df_news = ak.stock_news_em(symbol=stock_code)
            if not df_news.empty:
                return df_news
            else:
                print(f"从akshare获取新闻返回空数据，将使用模拟数据")
        
        # 如果akshare不可用或返回空数据，则使用模拟数据
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        # 创建模拟数据
        news_data = [
            {'发布时间': current_date, '新闻标题': '市场综述：A股震荡上行', '文章来源': '财经网'},
            {'发布时间': current_date, '新闻标题': '央行：保持流动性合理充裕', '文章来源': '央行网站'},
            {'发布时间': current_date, '新闻标题': '两市成交额突破万亿', '文章来源': '证券时报'}
        ]
        
        if stock_code:
            # 添加与特定股票相关的新闻
            news_data.append({'发布时间': current_date, 
                             '新闻标题': f'{stock_code}公司发布重要公告', 
                             '文章来源': '公司公告'})
        
        return pd.DataFrame(news_data)
    except Exception as e:
        print(f"获取新闻时出错: {e}")
        return pd.DataFrame()

def get_stock_indicators(stock_code, data_source='miniqmt'):
    """
    计算股票或板块技术指标并返回字典
    添加30分钟各类技术指标计算以及周线KDJ和RSI指标计算
    """
    # 获取数据
    daily_data = get_daily_data(stock_code, data_source)
    
    # 检查数据是否有效
    if daily_data.empty:
        return {"error": f"获取{stock_code} 的日线数据失败，请检查代码是否正确。"}
    
    # 确保日期列名正确处理
    if '日期' not in daily_data.columns and 'date' in daily_data.columns:
        daily_data.rename(columns={'date': '日期'}, inplace=True)
    
    # 获取周线数据（通过日线数据计算）
    weekly_data = get_weekly_data(stock_code, count=200)
    
    if weekly_data.empty:
        return {"error": f"计算{stock_code} 的周线数据失败，日线数据可能不足。"}
    
    # 获取需要的列名
    daily_cols = {
        'close': get_column_name(daily_data, 'close', '收盘'),
        'high': get_column_name(daily_data, 'high', '最高'),
        'low': get_column_name(daily_data, 'low', '最低'),
        'open': get_column_name(daily_data, 'open', '开盘'),
        'volume': get_column_name(daily_data, 'volume', '成交量'),
        'date': get_column_name(daily_data, '日期', '日期')
    }
    
    weekly_cols = {
        'close': get_column_name(weekly_data, 'close', '收盘'),
        'high': get_column_name(weekly_data, 'high', '最高'),
        'low': get_column_name(weekly_data, 'low', '最低'),
        'open': get_column_name(weekly_data, 'open', '开盘'),
        'volume': get_column_name(weekly_data, 'volume', '成交量'),
        'date': get_column_name(weekly_data, '日期', '日期')
    }
    
    # 验证必要的列是否都存在
    missing_daily = [k for k, v in daily_cols.items() if v is None]
    if missing_daily:
        return {"error": f"日线数据缺少必要的列: {missing_daily}，实际列名: {daily_data.columns.tolist()}"}
        
    missing_weekly = [k for k, v in weekly_cols.items() if v is None]
    if missing_weekly:
        return {"error": f"周线数据缺少必要的列: {missing_weekly}，实际列名: {weekly_data.columns.tolist()}"}
    
    # 计算各种指标
    daily_dif, daily_dea, daily_macd = calculate_macd(daily_data)
    # 重置索引
    daily_dif = daily_dif.reset_index(drop=True)
    daily_dea = daily_dea.reset_index(drop=True)
    daily_macd = daily_macd.reset_index(drop=True)
    
    # 记住原始排序方向
    original_weekly_order_descending = weekly_data['日期'].is_monotonic_decreasing
    
    # 统一转为升序计算
    weekly_data_asc = weekly_data.sort_values('日期', ascending=True)
    
    # 1. 计算MACD - 使用is_weekly=True标志
    weekly_dif_asc, weekly_dea_asc, weekly_macd_asc = calculate_macd(
        weekly_data_asc, is_weekly=True
    )
    
    # 根据原始排序方向处理结果
    if original_weekly_order_descending:
        # 将升序结果转为降序
        weekly_dif = weekly_dif_asc.iloc[::-1].reset_index(drop=True)
        weekly_dea = weekly_dea_asc.iloc[::-1].reset_index(drop=True)
        weekly_macd = weekly_macd_asc.iloc[::-1].reset_index(drop=True)
    else:
        # 保持升序
        weekly_dif = weekly_dif_asc.reset_index(drop=True)
        weekly_dea = weekly_dea_asc.reset_index(drop=True)
        weekly_macd = weekly_macd_asc.reset_index(drop=True)
    
    # 2. 计算布林带 - 使用is_weekly=True标志适应周线特有参数
    weekly_upper_asc, weekly_middle_asc, weekly_lower_asc = calculate_bollinger(weekly_data_asc)
    
    # 3. 计算MA均线
    weekly_ma_asc = calculate_ma(weekly_data_asc)  # 保持原变量名
    
    # 4. 计算周线KDJ (新增)
    weekly_k_asc, weekly_d_asc, weekly_j_asc = calculate_kdj(weekly_data_asc)
    
    # 5. 计算周线RSI (新增)
    weekly_rsi_asc = calculate_rsi(weekly_data_asc)
    
    # 保持升序排列，不再反转
    weekly_dif = weekly_dif.reset_index(drop=True)
    weekly_dea = weekly_dea.reset_index(drop=True)
    weekly_macd = weekly_macd.reset_index(drop=True)
    weekly_upper = weekly_upper_asc.reset_index(drop=True)
    weekly_middle = weekly_middle_asc.reset_index(drop=True)
    weekly_lower = weekly_lower_asc.reset_index(drop=True)
    
    # 重置新增指标的索引
    weekly_k = weekly_k_asc.reset_index(drop=True)
    weekly_d = weekly_d_asc.reset_index(drop=True)
    weekly_j = weekly_j_asc.reset_index(drop=True)
    
    # MA均线 - 使用原变量名
    weekly_ma = {}  # 创建新字典
    for key, value in weekly_ma_asc.items():
        weekly_ma[key] = value.reset_index(drop=True)  # 使用新字典存储
    
    # RSI指标 - 新增
    weekly_rsi = {}  # 创建新字典
    for key, value in weekly_rsi_asc.items():
        weekly_rsi[key] = value.reset_index(drop=True)  # 使用新字典存储
    
    k, d, j = calculate_kdj(daily_data)
    # 重置索引
    k = k.reset_index(drop=True)
    d = d.reset_index(drop=True)
    j = j.reset_index(drop=True)
    
    daily_upper, daily_middle, daily_lower = calculate_bollinger(daily_data)
    # 重置索引
    daily_upper = daily_upper.reset_index(drop=True)
    daily_middle = daily_middle.reset_index(drop=True)
    daily_lower = daily_lower.reset_index(drop=True)
    
    daily_ma = calculate_ma(daily_data)
    # 重置索引
    for key in daily_ma:
        daily_ma[key] = daily_ma[key].reset_index(drop=True)
    
    rsi_data = calculate_rsi(daily_data)
    # 重置索引
    for key in rsi_data:
        rsi_data[key] = rsi_data[key].reset_index(drop=True)
    
    # 计算成交量均线 - 新增（必须在使用前先计算）
    daily_volume_ma = calculate_volume_ma(daily_data, periods=[5, 10])
    # 重置索引
    for key in daily_volume_ma:
        daily_volume_ma[key] = daily_volume_ma[key].reset_index(drop=True)
    
    # 计算周线成交量均线 - 新增（必须在使用前先计算）
    weekly_volume_ma = calculate_volume_ma(weekly_data, periods=[5, 10])
    # 重置索引
    for key in weekly_volume_ma:
        weekly_volume_ma[key] = weekly_volume_ma[key].reset_index(drop=True)
    
    # 计算MACD得分 - 日线（使用扩展版分析，包含成交量指标）
    recent_daily_dif = []
    recent_daily_macd = []
    recent_daily_volume = []
    recent_daily_mavol5 = []
    
    for i in range(min(10, len(daily_data))):
        idx = len(daily_data) - 1 - i  # 从最新日期开始
        recent_daily_dif.append(daily_dif[idx])
        recent_daily_macd.append(daily_macd[idx])
        recent_daily_volume.append(daily_data.iloc[idx][daily_cols['volume']])
        recent_daily_mavol5.append(daily_volume_ma["VMA5"][idx])
        
    # 准备全面技术分析所需的数据
    recent_daily_dea = []
    recent_daily_close = []
    recent_daily_ma5 = []
    recent_daily_ma10 = []
    recent_daily_rsi6 = []
    
    for i in range(min(10, len(daily_data))):
        idx = len(daily_data) - 1 - i  # 从最新日期开始
        recent_daily_dea.append(daily_dea[idx])
        recent_daily_close.append(daily_data.iloc[idx][daily_cols['close']])
        recent_daily_ma5.append(daily_ma["MA5"][idx])
        recent_daily_ma10.append(daily_ma["MA10"][idx])
        recent_daily_rsi6.append(rsi_data["RSI6"][idx])
    
    daily_score, daily_comments = analyze_comprehensive_indicators(
        recent_daily_dif, recent_daily_dea, recent_daily_macd, 
        recent_daily_volume, recent_daily_mavol5, recent_daily_close,
        recent_daily_ma5, recent_daily_ma10, recent_daily_rsi6
    )
    
    # 计算MACD得分 - 周线（使用扩展版分析，包含成交量指标）
    recent_weekly_dif = []
    recent_weekly_macd = []
    recent_weekly_volume = []
    recent_weekly_mavol5 = []
    
    for i in range(min(10, len(weekly_data))):
        idx = len(weekly_data) - 1 - i  # 修正：从最新日期开始
        recent_weekly_dif.append(weekly_dif[idx])
        recent_weekly_macd.append(weekly_macd[idx])
        recent_weekly_volume.append(weekly_data.iloc[idx][weekly_cols['volume']])
        recent_weekly_mavol5.append(weekly_volume_ma["VMA5"][idx])
        
    # 准备周线全面技术分析所需的数据
    recent_weekly_dea = []
    recent_weekly_close = []
    recent_weekly_ma5 = []
    recent_weekly_ma10 = []
    recent_weekly_rsi6 = []
    
    for i in range(min(10, len(weekly_data))):
        idx = len(weekly_data) - 1 - i  # 修正：从最新日期开始
        recent_weekly_dea.append(weekly_dea[idx])
        recent_weekly_close.append(weekly_data.iloc[idx][weekly_cols['close']])
        recent_weekly_ma5.append(weekly_ma["MA5"][idx])
        recent_weekly_ma10.append(weekly_ma["MA10"][idx])
        recent_weekly_rsi6.append(weekly_rsi["RSI6"][idx])
        
    weekly_score, weekly_comments = analyze_comprehensive_indicators(
        recent_weekly_dif, recent_weekly_dea, recent_weekly_macd, 
        recent_weekly_volume, recent_weekly_mavol5, recent_weekly_close,
        recent_weekly_ma5, recent_weekly_ma10, recent_weekly_rsi6
    )
    
    # 获取相关新闻 - 仅对股票
    news_df = pd.DataFrame()
    if not stock_code.startswith(('880', '881', '399')):
        news_df = get_stock_news(stock_code)
    
    # 获取30分钟线数据（增加数据量确保包含完整历史数据）
    start_time = time.time()
    timeout = 30  # 30秒超时
    
    try:
        min30_data = get_miniqmt_data(stock_code, period='30m', count=1000)
        
        # 检查是否超时
        if time.time() - start_time > timeout:
            min30_data = pd.DataFrame()
    except Exception as e:
        min30_data = pd.DataFrame()
    
    # 初始化30分钟技术指标结果
    min30_macd_results = {"dif": [], "dea": [], "macd": []}
    min30_kdj_results = {"k": [], "d": [], "j": []}
    min30_bollinger_results = {"upper": [], "middle": [], "lower": []}
    min30_rsi_results = {"RSI6": [], "RSI12": [], "RSI24": []}
    min30_ma_results = {"MA5": [], "MA10": [], "MA60": []}
    min30_volume_ma_results = {"VMA5": [], "VMA10": []}
    min30_score = 0
    min30_comments = []
    
    if not min30_data.empty:
        # 检查30分钟数据排序情况
        is_min30_ascending = pd.to_datetime(min30_data['日期']).is_monotonic_increasing
        is_min30_descending = pd.to_datetime(min30_data['日期']).is_monotonic_decreasing
        
        # 确保列名一致
        if '收盘' in min30_data.columns:
            close_col = '收盘'
            high_col = '最高'
            low_col = '最低'
        else:
            close_col = get_column_name(min30_data, 'close', '收盘')
            high_col = get_column_name(min30_data, 'high', '最高')
            low_col = get_column_name(min30_data, 'low', '最低')
            
        if close_col is not None and high_col is not None and low_col is not None:
            # 计算30分钟MACD
            min30_dif, min30_dea, min30_macd = calc_macd(min30_data[close_col])
            
            # 计算30分钟KDJ
            min30_k, min30_d, min30_j = calc_kdj(
                min30_data[high_col], 
                min30_data[low_col], 
                min30_data[close_col]
            )
            
            # 计算30分钟布林带
            min30_upper, min30_middle, min30_lower = calc_bollinger(min30_data[close_col])
            
            # 计算30分钟RSI
            min30_rsi_dict = calculate_rsi(min30_data)
            
            # 计算30分钟MA
            min30_ma_dict = calculate_ma(min30_data)
            
            # 计算30分钟成交量均线
            min30_volume_ma_dict = calculate_volume_ma(min30_data, periods=[5, 10])
            
            # 重置索引以保持对齐
            min30_dif = min30_dif.reset_index(drop=True)
            min30_dea = min30_dea.reset_index(drop=True)
            min30_macd = min30_macd.reset_index(drop=True)
            min30_k = min30_k.reset_index(drop=True)
            min30_d = min30_d.reset_index(drop=True)
            min30_j = min30_j.reset_index(drop=True)
            min30_upper = min30_upper.reset_index(drop=True)
            min30_middle = min30_middle.reset_index(drop=True)
            min30_lower = min30_lower.reset_index(drop=True)
            
            # 重置成交量均线索引
            for key in min30_volume_ma_dict:
                min30_volume_ma_dict[key] = min30_volume_ma_dict[key].reset_index(drop=True)
            
            # 获取最新5个周期 - 根据排序方向调整索引
            for i in range(min(10, len(min30_data))):
                if is_min30_descending:
                    idx = i  # 降序：前5行是最新数据
                else:
                    idx = len(min30_data) - 1 - i  # 升序：从末尾开始
                
                # 确保索引在有效范围内
                if idx < len(min30_dif):
                    date = min30_data.iloc[idx]['日期']
                    if hasattr(date, 'strftime'):
                        date_str = date.strftime('%Y-%m-%d %H:%M')
                    else:
                        date_str = str(date)
                    
                    # MACD数据
                    min30_macd_results["dif"].append({
                        "date": date_str,
                        "value": float(min30_dif.iloc[idx])
                    })
                    min30_macd_results["dea"].append({
                        "date": date_str,
                        "value": float(min30_dea.iloc[idx])
                    })
                    min30_macd_results["macd"].append({
                        "date": date_str,
                        "value": float(min30_macd.iloc[idx])
                    })
                    
                    # KDJ数据
                    min30_kdj_results["k"].append({
                        "date": date_str,
                        "value": float(min30_k.iloc[idx])
                    })
                    min30_kdj_results["d"].append({
                        "date": date_str,
                        "value": float(min30_d.iloc[idx])
                    })
                    min30_kdj_results["j"].append({
                        "date": date_str,
                        "value": float(min30_j.iloc[idx])
                    })
                    
                    # 布林带数据
                    min30_bollinger_results["upper"].append({
                        "date": date_str,
                        "value": float(min30_upper.iloc[idx])
                    })
                    min30_bollinger_results["middle"].append({
                        "date": date_str,
                        "value": float(min30_middle.iloc[idx])
                    })
                    min30_bollinger_results["lower"].append({
                        "date": date_str,
                        "value": float(min30_lower.iloc[idx])
                    })
                    
                    # RSI数据
                    for period in ["RSI6", "RSI12", "RSI24"]:
                        if period in min30_rsi_dict:
                            min30_rsi_results[period].append({
                                "date": date_str,
                                "value": float(min30_rsi_dict[period].iloc[idx])
                            })
                    
                    # MA数据
                    for period in ["MA5", "MA10", "MA60"]:
                        if period in min30_ma_dict:
                            min30_ma_results[period].append({
                                "date": date_str,
                                "value": float(min30_ma_dict[period].iloc[idx])
                            })
                    
                    # 成交量均线数据
                    for period in ["VMA5", "VMA10"]:
                        if period in min30_volume_ma_dict:
                            min30_volume_ma_results[period].append({
                                "date": date_str,
                                "value": float(min30_volume_ma_dict[period].iloc[idx])
                            })
                else:
                    print(f"警告：索引 {idx} 超出范围 {len(min30_dif)}")
    
    # 计算30分钟MACD得分（统一使用全面技术分析）
    recent_min30_dif = []
    recent_min30_macd = []
    recent_min30_volume = []
    recent_min30_mavol5 = []
    
    for i in range(min(10, len(min30_macd_results["dif"]))):
        recent_min30_dif.append(min30_macd_results["dif"][i]["value"])
        recent_min30_macd.append(min30_macd_results["macd"][i]["value"])
        
        # 添加成交量数据 - 修正数据获取方式
        if i < len(min30_data):
            # 根据30分钟数据的排序方向获取对应的成交量
            if is_min30_descending:
                volume_col = get_column_name(min30_data, 'volume', '成交量')
                if volume_col:
                    recent_min30_volume.append(min30_data.iloc[i][volume_col])
                else:
                    recent_min30_volume.append(0)
            else:
                volume_col = get_column_name(min30_data, 'volume', '成交量')
                if volume_col:
                    recent_min30_volume.append(min30_data.iloc[len(min30_data) - 1 - i][volume_col])
                else:
                    recent_min30_volume.append(0)
            
            if i < len(min30_volume_ma_results["VMA5"]):
                recent_min30_mavol5.append(min30_volume_ma_results["VMA5"][i]["value"])
            else:
                recent_min30_mavol5.append(0)
        
    # 统一使用全面技术分析（不再使用简化版本）
    if (len(recent_min30_dif) >= 2 and len(min30_macd_results["dea"]) >= 2 and 
        len(min30_ma_results["MA5"]) >= 2 and len(min30_ma_results["MA10"]) >= 2 and 
        len(min30_rsi_results["RSI6"]) >= 2):
        
        recent_min30_dea = [item["value"] for item in min30_macd_results["dea"][:10]]
        recent_min30_close = []
        recent_min30_ma5 = [item["value"] for item in min30_ma_results["MA5"][:10]]
        recent_min30_ma10 = [item["value"] for item in min30_ma_results["MA10"][:10]]
        recent_min30_rsi6 = [item["value"] for item in min30_rsi_results["RSI6"][:10]]
        
        # 获取30分钟收盘价数据 - 修正数据获取方式
        for i in range(min(10, len(min30_data))):
            if is_min30_descending:
                close_col = get_column_name(min30_data, 'close', '收盘')
                if close_col:
                    recent_min30_close.append(min30_data.iloc[i][close_col])
                else:
                    recent_min30_close.append(0)
            else:
                close_col = get_column_name(min30_data, 'close', '收盘')
                if close_col:
                    recent_min30_close.append(min30_data.iloc[len(min30_data) - 1 - i][close_col])
                else:
                    recent_min30_close.append(0)
        
        # 确保所有数据列表长度一致
        min_len = min(len(recent_min30_dif), len(recent_min30_dea), len(recent_min30_macd),
                     len(recent_min30_volume), len(recent_min30_mavol5), len(recent_min30_close),
                     len(recent_min30_ma5), len(recent_min30_ma10), len(recent_min30_rsi6))
        
        if min_len >= 2:
            min30_score, min30_comments = analyze_comprehensive_indicators(
                recent_min30_dif[:min_len], recent_min30_dea[:min_len], recent_min30_macd[:min_len], 
                recent_min30_volume[:min_len], recent_min30_mavol5[:min_len], recent_min30_close[:min_len],
                recent_min30_ma5[:min_len], recent_min30_ma10[:min_len], recent_min30_rsi6[:min_len]
            )
        else:
            min30_score, min30_comments = 0, ["30分钟数据不足，无法进行全面技术分析"]
    else:
        min30_score, min30_comments = 0, ["30分钟数据不足，无法进行全面技术分析"]
    
    # 计算支撑位和压力位 (使用新的连续性函数，压力位排除最近4个交易日)
    support_resistance = find_continuous_sr_levels(daily_data, n=5, tolerance_percent=0.02, exclude_recent_days=4)
    
    # 构建结果字典（添加30分钟技术指标字段）
    result = {
        "code": stock_code,
        "is_block": stock_code.startswith(('880', '881', '399')),
        "daily_data": [],
        "weekly_data": [],  # 这里将存储52周数据
        "daily_macd": {
            "dif": [],
            "dea": [],
            "macd": [],
            "score": daily_score,
            "comments": daily_comments
        },
        "weekly_macd": {
            "dif": [],
            "dea": [],
            "macd": [],
            "score": weekly_score,
            "comments": weekly_comments
        },
        "daily_kdj": {
            "k": [],
            "d": [],
            "j": []
        },
        "daily_bollinger": {
            "upper": [],
            "middle": [],
            "lower": []
        },
        "weekly_bollinger": {
            "upper": [],
            "middle": [],
            "lower": []
        },
        "daily_ma": {
            "MA5": [],
            "MA10": [],
            "MA60": []
        },
        "weekly_ma": {
            "MA5": [],
            "MA10": [],
            "MA60": []
        },
        "daily_rsi": {
            "RSI6": [],
            "RSI12": [],
            "RSI24": []
        },
        # 新增周线KDJ指标
        "weekly_kdj": {
            "k": [],
            "d": [],
            "j": []
        },
        # 新增周线RSI指标
        "weekly_rsi": {
            "RSI6": [],
            "RSI12": [],
            "RSI24": []
        },
        # 新增支撑位和压力位区域
        "support_resistance": support_resistance,
        # 保存原始日线数据用于颈线计算
        "raw_daily_data": daily_data,
        "news": [],
        "min30_data": [],  # 存储30分钟线数据
        "min30_macd": {
            "dif": min30_macd_results["dif"],
            "dea": min30_macd_results["dea"],
            "macd": min30_macd_results["macd"],
            "score": min30_score,
            "comments": min30_comments
        },
        # 新增30分钟技术指标字段
        "min30_kdj": {
            "k": min30_kdj_results["k"],
            "d": min30_kdj_results["d"],
            "j": min30_kdj_results["j"]
        },
        "min30_bollinger": {
            "upper": min30_bollinger_results["upper"],
            "middle": min30_bollinger_results["middle"],
            "lower": min30_bollinger_results["lower"]
        },
        "min30_rsi": {
            "RSI6": min30_rsi_results["RSI6"],
            "RSI12": min30_rsi_results["RSI12"],
            "RSI24": min30_rsi_results["RSI24"]
        },
        "min30_ma": {
            "MA5": min30_ma_results["MA5"],
            "MA10": min30_ma_results["MA10"],
            "MA60": min30_ma_results["MA60"]
        },
        "daily_volume_ma": {
            "VMA5": [],
            "VMA10": []
        },
        "weekly_volume_ma": {
            "VMA5": [],
            "VMA10": []
        },
        "min30_volume_ma": {
            "VMA5": min30_volume_ma_results["VMA5"],
            "VMA10": min30_volume_ma_results["VMA10"]
        }
    }
    
    # 检查日线数据排序情况
    is_daily_ascending = pd.to_datetime(daily_data[daily_cols['date']]).is_monotonic_increasing
    is_daily_descending = pd.to_datetime(daily_data[daily_cols['date']]).is_monotonic_decreasing
    
    # 填充日线数据 - 统一使用升序排列后的索引
    # 最新数据在DataFrame的最后一行
    for i in range(1, 6):  # 获取最后5条数据 (最新数据)
        idx = len(daily_data) - i
        if idx < 0:  # 确保索引有效
            continue
            
        date = daily_data.iloc[idx][daily_cols['date']]
        date_str = date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date)
        
        result["daily_data"].append({
            "date": date_str,
            "open": float(daily_data.iloc[idx][daily_cols['open']]),
            "close": float(daily_data.iloc[idx][daily_cols['close']]),
            "high": float(daily_data.iloc[idx][daily_cols['high']]),
            "low": float(daily_data.iloc[idx][daily_cols['low']]),
            "volume": float(daily_data.iloc[idx][daily_cols['volume']])
        })
        
        # 填充MACD数据
        result["daily_macd"]["dif"].append({
            "date": date_str,
            "value": float(daily_dif[idx])
        })
        result["daily_macd"]["dea"].append({
            "date": date_str,
            "value": float(daily_dea[idx])
        })
        result["daily_macd"]["macd"].append({
            "date": date_str,
            "value": float(daily_macd[idx])
        })
        
        # 填充布林带数据
        result["daily_bollinger"]["upper"].append({
            "date": date_str,
            "value": float(daily_upper[idx])
        })
        result["daily_bollinger"]["middle"].append({
            "date": date_str,
            "value": float(daily_middle[idx])
        })
        result["daily_bollinger"]["lower"].append({
            "date": date_str,
            "value": float(daily_lower[idx])
        })
        
        # 填充均线数据
        result["daily_ma"]["MA5"].append({
            "date": date_str,
            "value": float(daily_ma["MA5"][idx])
        })
        result["daily_ma"]["MA10"].append({
            "date": date_str,
            "value": float(daily_ma["MA10"][idx])
        })
        result["daily_ma"]["MA60"].append({
            "date": date_str,
            "value": float(daily_ma["MA60"][idx])
        })
        
        # 只取前5个周期的KDJ和RSI
        if i < 5:
            result["daily_kdj"]["k"].append({
                "date": date_str,
                "value": float(k[idx])
            })
            result["daily_kdj"]["d"].append({
                "date": date_str,
                "value": float(d[idx])
            })
            result["daily_kdj"]["j"].append({
                "date": date_str,
                "value": float(j[idx])
            })
            
            result["daily_rsi"]["RSI6"].append({
                "date": date_str,
                "value": float(rsi_data["RSI6"][idx])
            })
            result["daily_rsi"]["RSI12"].append({
                "date": date_str,
                "value": float(rsi_data["RSI12"][idx])
            })
            result["daily_rsi"]["RSI24"].append({
                "date": date_str,
                "value": float(rsi_data["RSI24"][idx])
            })
        
        # 填充成交量均线数据 - 新增
        result["daily_volume_ma"]["VMA5"].append({
            "date": date_str,
            "value": float(daily_volume_ma["VMA5"][idx])
        })
        result["daily_volume_ma"]["VMA10"].append({
            "date": date_str,
            "value": float(daily_volume_ma["VMA10"][idx])
        })
    
    # 检查周线数据排序情况
    is_weekly_ascending = pd.to_datetime(weekly_data[weekly_cols['date']]).is_monotonic_increasing
    is_weekly_descending = pd.to_datetime(weekly_data[weekly_cols['date']]).is_monotonic_decreasing
    
    # 填充周线数据 - 自动检测排序方式并适应
    for i in range(min(5, len(weekly_data))):  # 修改为5个周期
        if is_weekly_descending:
            idx = i
        else:
            idx = len(weekly_data) - 1 - i
            
        date = weekly_data.iloc[idx][weekly_cols['date']]
        date_str = date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date)
        
        result["weekly_data"].append({
            "date": date_str,
            "open": float(weekly_data.iloc[idx][weekly_cols['open']]),
            "close": float(weekly_data.iloc[idx][weekly_cols['close']]),
            "high": float(weekly_data.iloc[idx][weekly_cols['high']]),
            "low": float(weekly_data.iloc[idx][weekly_cols['low']]),
            "volume": float(weekly_data.iloc[idx][weekly_cols['volume']])
        })
        
        # 填充周线MACD数据
        result["weekly_macd"]["dif"].append({
            "date": date_str,
            "value": float(weekly_dif[idx])
        })
        result["weekly_macd"]["dea"].append({
            "date": date_str,
            "value": float(weekly_dea[idx])
        })
        result["weekly_macd"]["macd"].append({
            "date": date_str,
            "value": float(weekly_macd[idx])
        })
        
        # 填充周线布林带数据
        result["weekly_bollinger"]["upper"].append({
            "date": date_str,
            "value": float(weekly_upper[idx])
        })
        result["weekly_bollinger"]["middle"].append({
            "date": date_str,
            "value": float(weekly_middle[idx])
        })
        result["weekly_bollinger"]["lower"].append({
            "date": date_str,
            "value": float(weekly_lower[idx])
        })
        
        # 填充周线均线数据
        result["weekly_ma"]["MA5"].append({
            "date": date_str,
            "value": float(weekly_ma["MA5"][idx])
        })
        result["weekly_ma"]["MA10"].append({
            "date": date_str,
            "value": float(weekly_ma["MA10"][idx])
        })
        result["weekly_ma"]["MA60"].append({
            "date": date_str,
            "value": float(weekly_ma["MA60"][idx])
        })
        
        # 填充周线KDJ数据 (新增)
        result["weekly_kdj"]["k"].append({
            "date": date_str,
            "value": float(weekly_k[idx])
        })
        result["weekly_kdj"]["d"].append({
            "date": date_str,
            "value": float(weekly_d[idx])
        })
        result["weekly_kdj"]["j"].append({
            "date": date_str,
            "value": float(weekly_j[idx])
        })
        
        # 填充周线RSI数据 (新增)
        result["weekly_rsi"]["RSI6"].append({
            "date": date_str,
            "value": float(weekly_rsi["RSI6"][idx])
        })
        result["weekly_rsi"]["RSI12"].append({
            "date": date_str,
            "value": float(weekly_rsi["RSI12"][idx])
        })
        result["weekly_rsi"]["RSI24"].append({
            "date": date_str,
            "value": float(weekly_rsi["RSI24"][idx])
        })
        
        # 填充周线成交量均线数据 - 新增
        result["weekly_volume_ma"]["VMA5"].append({
            "date": date_str,
            "value": float(weekly_volume_ma["VMA5"][idx])
        })
        result["weekly_volume_ma"]["VMA10"].append({
            "date": date_str,
            "value": float(weekly_volume_ma["VMA10"][idx])
        })
    
    # 填充新闻数据 - 仅对股票
    if not stock_code.startswith(('880', '881', '399')):
        for i in range(min(5, len(news_df))):  # 修改为5个周期
            news = news_df.iloc[i]
            result["news"].append({
                "date": news['发布时间'],
                "title": news['新闻标题'],
                "source": news['文章来源']
            })
    
    # 填充30分钟线数据 - 根据排序方向调整索引
    if not min30_data.empty:
        for i in range(min(5, len(min30_data))):
            if is_min30_descending:
                idx = i  # 降序：前5行是最新数据
            else:
                idx = len(min30_data) - 1 - i  # 升序：从末尾开始
                
            row = min30_data.iloc[idx]
            # 确保日期格式正确
            if hasattr(row['日期'], 'strftime'):
                date_str = row['日期'].strftime('%Y-%m-%d %H:%M')
            else:
                date_str = str(row['日期'])
                
            result["min30_data"].append({
                "date": date_str,
                "open": float(row['开盘']),
                "close": float(row['收盘']),
                "high": float(row['最高']),
                "low": float(row['最低']),
                "volume": float(row['成交量'])
            })
    
    # ========== 添加背离分析 ==========
    
    # 1. 日线背离分析（准备100个周期的数据）
    daily_divergence_comments = []
    if len(daily_data) >= 10:  # 至少需要10个周期进行分析
        # 准备100个周期的日线数据（从新到旧排序）
        daily_data_for_divergence = daily_data.copy()
        if not is_daily_descending:
            # 如果数据是升序，需要反转为降序（从新到旧）
            daily_data_for_divergence = daily_data_for_divergence.iloc[::-1].reset_index(drop=True)
        
        # 准备100个周期的MACD数据（从新到旧）
        macd_values_for_divergence = []
        volume_values_for_divergence = []
        volume_ma5_values_for_divergence = []
        
        max_periods = min(100, len(daily_data_for_divergence))
        for i in range(max_periods):
            if i < len(daily_macd):
                if is_daily_descending:
                    macd_values_for_divergence.append(daily_macd[i])
                    volume_values_for_divergence.append(daily_data_for_divergence.iloc[i][daily_cols['volume']])
                    # 获取对应的MAVOL5数据
                    if i < len(daily_volume_ma["VMA5"]):
                        volume_ma5_values_for_divergence.append(daily_volume_ma["VMA5"][i])
                    else:
                        volume_ma5_values_for_divergence.append(0)
                else:
                    # 如果原始数据是升序，需要从末尾开始取
                    macd_idx = len(daily_macd) - 1 - i
                    macd_values_for_divergence.append(daily_macd[macd_idx])
                    volume_values_for_divergence.append(daily_data_for_divergence.iloc[i][daily_cols['volume']])
                    # 获取对应的MAVOL5数据
                    if macd_idx < len(daily_volume_ma["VMA5"]):
                        volume_ma5_values_for_divergence.append(daily_volume_ma["VMA5"][macd_idx])
                    else:
                        volume_ma5_values_for_divergence.append(0)
        
        # 调用背离分析
        daily_divergence_comments = analyze_divergence(
            daily_data_for_divergence.head(max_periods),
            macd_values_for_divergence,
            volume_values_for_divergence,
            volume_ma5_values_for_divergence,
            'daily'
        )
    
    # 2. 周线背离分析（准备100个周期的数据）
    weekly_divergence_comments = []
    if len(weekly_data) >= 10:  # 至少需要10个周期进行分析
        # 准备100个周期的周线数据（从新到旧排序）
        weekly_data_for_divergence = weekly_data.copy()
        if not is_weekly_descending:
            # 如果数据是升序，需要反转为降序（从新到旧）
            weekly_data_for_divergence = weekly_data_for_divergence.iloc[::-1].reset_index(drop=True)
        
        # 准备100个周期的MACD数据（从新到旧）
        weekly_macd_values_for_divergence = []
        weekly_volume_values_for_divergence = []
        weekly_volume_ma5_values_for_divergence = []
        
        max_periods = min(100, len(weekly_data_for_divergence))
        for i in range(max_periods):
            if i < len(weekly_macd):
                if is_weekly_descending:
                    weekly_macd_values_for_divergence.append(weekly_macd[i])
                    weekly_volume_values_for_divergence.append(weekly_data_for_divergence.iloc[i][weekly_cols['volume']])
                    # 获取对应的MAVOL5数据
                    if i < len(weekly_volume_ma["VMA5"]):
                        weekly_volume_ma5_values_for_divergence.append(weekly_volume_ma["VMA5"][i])
                    else:
                        weekly_volume_ma5_values_for_divergence.append(0)
                else:
                    # 如果原始数据是升序，需要从末尾开始取
                    macd_idx = len(weekly_macd) - 1 - i
                    weekly_macd_values_for_divergence.append(weekly_macd[macd_idx])
                    weekly_volume_values_for_divergence.append(weekly_data_for_divergence.iloc[i][weekly_cols['volume']])
                    # 获取对应的MAVOL5数据
                    if macd_idx < len(weekly_volume_ma["VMA5"]):
                        weekly_volume_ma5_values_for_divergence.append(weekly_volume_ma["VMA5"][macd_idx])
                    else:
                        weekly_volume_ma5_values_for_divergence.append(0)
        
        # 调用背离分析
        weekly_divergence_comments = analyze_divergence(
            weekly_data_for_divergence.head(max_periods),
            weekly_macd_values_for_divergence,
            weekly_volume_values_for_divergence,
            weekly_volume_ma5_values_for_divergence,
            'weekly'
        )
    
    # 3. 30分钟背离分析（准备100个周期的数据）
    min30_divergence_comments = []
    if not min30_data.empty and len(min30_data) >= 10:
        # 准备100个周期的30分钟数据（从新到旧排序）
        min30_data_for_divergence = min30_data.copy()
        if not is_min30_descending:
            # 如果数据是升序，需要反转为降序（从新到旧）
            min30_data_for_divergence = min30_data_for_divergence.iloc[::-1].reset_index(drop=True)
        
        # 准备100个周期的MACD数据（从新到旧）
        min30_macd_values_for_divergence = []
        min30_volume_values_for_divergence = []
        min30_volume_ma5_values_for_divergence = []
        
        max_periods = min(100, len(min30_data_for_divergence))
        for i in range(max_periods):
            if i < len(min30_macd):
                if is_min30_descending:
                    min30_macd_values_for_divergence.append(min30_macd[i])
                    volume_col = get_column_name(min30_data_for_divergence, 'volume', '成交量')
                    if volume_col:
                        min30_volume_values_for_divergence.append(min30_data_for_divergence.iloc[i][volume_col])
                    else:
                        min30_volume_values_for_divergence.append(0)
                    # 获取对应的MAVOL5数据
                    if i < len(min30_volume_ma_dict["VMA5"]):
                        min30_volume_ma5_values_for_divergence.append(min30_volume_ma_dict["VMA5"][i])
                    else:
                        min30_volume_ma5_values_for_divergence.append(0)
                else:
                    # 如果原始数据是升序，需要从末尾开始取
                    macd_idx = len(min30_macd) - 1 - i
                    min30_macd_values_for_divergence.append(min30_macd[macd_idx])
                    volume_col = get_column_name(min30_data_for_divergence, 'volume', '成交量')
                    if volume_col:
                        min30_volume_values_for_divergence.append(min30_data_for_divergence.iloc[i][volume_col])
                    else:
                        min30_volume_values_for_divergence.append(0)
                    # 获取对应的MAVOL5数据
                    if macd_idx < len(min30_volume_ma_dict["VMA5"]):
                        min30_volume_ma5_values_for_divergence.append(min30_volume_ma_dict["VMA5"][macd_idx])
                    else:
                        min30_volume_ma5_values_for_divergence.append(0)
        
        # 调用背离分析
        min30_divergence_comments = analyze_divergence(
            min30_data_for_divergence.head(max_periods),
            min30_macd_values_for_divergence,
            min30_volume_values_for_divergence,
            min30_volume_ma5_values_for_divergence,
            'min30'
        )
    
    # 添加背离分析结果到返回字典
    result["divergence_analysis"] = {
        "daily": daily_divergence_comments,
        "weekly": weekly_divergence_comments,
        "min30": min30_divergence_comments
    }
    
    return result

def analyze_stock(stock_code, data_source='miniqmt'):
    """
    分析股票或板块技术指标
    使用新格式输出整合的技术指标数据
    """
    # 获取技术指标数据
    indicators = get_stock_indicators(stock_code, data_source)
    
    # 检查是否有错误
    if "error" in indicators:
        return indicators["error"]
    
    # 添加个股决选文件信息
    header = f"{stock_code} 的技术指标分析："
    
    # 构建结果字符串
    result = f"{header}\n\n"
    
    # 1. 短线趋势行情：最近5个交易日的K线/成交量行情
    result += "1. 短线趋势行情：最近5个交易日的K线/成交量行情\n"
    for i in range(len(indicators["daily_data"])):
        day_data = indicators["daily_data"][i]
        # 成交量相关指标使用倒数第6到倒数第2的数据（排除最新周期）
        vma5 = indicators["daily_volume_ma"]["VMA5"][i]["value"] if i < len(indicators["daily_volume_ma"]["VMA5"]) else 0
        vma10 = indicators["daily_volume_ma"]["VMA10"][i]["value"] if i < len(indicators["daily_volume_ma"]["VMA10"]) else 0
        result += f"   {day_data['date']}: 开盘={day_data['open']:.2f}, 收盘={day_data['close']:.2f}, 最高={day_data['high']:.2f}, 最低={day_data['low']:.2f}, 成交量={day_data['volume']}, MAVOL5={vma5:.2f}, MAVOL10={vma10:.2f}\n"
    
    # 2. 短线技术指标：最近5个交易日的MACD/RSI/KDJ/布林带/均线等指标
    result += "\n2. 短线技术指标：最近5个交易日的MACD/RSI/KDJ/布林带/均线等指标\n"
    for i in range(min(5, len(indicators["daily_macd"]["dif"]))):
        date = indicators["daily_macd"]["dif"][i]["date"]
        
        # MACD数据
        dif = indicators["daily_macd"]["dif"][i]["value"]
        dea = indicators["daily_macd"]["dea"][i]["value"]
        macd = indicators["daily_macd"]["macd"][i]["value"]
        
        # KDJ数据
        k_val = indicators["daily_kdj"]["k"][i]["value"] if i < len(indicators["daily_kdj"]["k"]) else 0
        d_val = indicators["daily_kdj"]["d"][i]["value"] if i < len(indicators["daily_kdj"]["d"]) else 0
        j_val = indicators["daily_kdj"]["j"][i]["value"] if i < len(indicators["daily_kdj"]["j"]) else 0
        
        # 布林带数据
        upper = indicators["daily_bollinger"]["upper"][i]["value"]
        middle = indicators["daily_bollinger"]["middle"][i]["value"]
        lower = indicators["daily_bollinger"]["lower"][i]["value"]
        
        # RSI数据
        rsi6 = indicators["daily_rsi"]["RSI6"][i]["value"] if i < len(indicators["daily_rsi"]["RSI6"]) else 0
        rsi12 = indicators["daily_rsi"]["RSI12"][i]["value"] if i < len(indicators["daily_rsi"]["RSI12"]) else 0
        rsi24 = indicators["daily_rsi"]["RSI24"][i]["value"] if i < len(indicators["daily_rsi"]["RSI24"]) else 0
        
        # 均线数据
        ma5 = indicators["daily_ma"]["MA5"][i]["value"]
        ma10 = indicators["daily_ma"]["MA10"][i]["value"]
        ma60 = indicators["daily_ma"]["MA60"][i]["value"]
        
        # 输出整合的数据
        result += f"   {date}: MACD.DIF={dif:.4f}, MACD.DEA={dea:.4f}, MACD.MACD={macd:.4f}\n"
        result += f"   KDJ.K={k_val:.2f}, KDJ.D={d_val:.2f}, KDJ.J={j_val:.2f}\n"
        result += f"   布林带.上轨={upper:.2f}, 布林带.中轨={middle:.2f}, 布林带.下轨={lower:.2f}\n"
        result += f"   RSI.RSI6={rsi6:.2f}, RSI.RSI12={rsi12:.2f}, RSI.RSI24={rsi24:.2f}\n"
        result += f"   MA.MA5={ma5:.2f}, MA.MA10={ma10:.2f}, MA.MA60={ma60:.2f}\n"
        result += "\n"
    
    # 添加日线MACD分析结果
    result += "   短线技术指标分析结果：\n"
    
    # 定义输出顺序和标题
    category_map = {
        'MACD': 'MACD',
        'MA': '均线/MA',
        'RSI': 'RSI',
        '成交量': '成交量'
    }

    # 1. 首先处理并打印MACD和总分
    result += f"   {category_map['MACD']}：\n"
    macd_comments = indicators["daily_macd"]["comments"].get('MACD')
    if macd_comments:
        for comment in macd_comments:
            result += f"   - {comment}\n"
    else:
        result += "   - 未发现MACD相关信号\n"

    # 2. 接着打印其他类别的分析结果
    for category_key, category_title in category_map.items():
        if category_key == 'MACD':
            continue  # MACD已经处理过了
        
        comments = indicators["daily_macd"]["comments"].get(category_key)
        if comments:
            result += f"   {category_title}：\n"
            for comment in comments:
                result += f"   - {comment}\n"
    
    # 添加日线背离分析结果
    if "divergence_analysis" in indicators and indicators["divergence_analysis"]["daily"]:
        result += "   背离分析：\n"
        for comment in indicators["divergence_analysis"]["daily"]:
            result += f"   - {comment}\n"
    else:
        result += "   背离分析：\n   - 未发现明显背离现象\n"

    
    # 3. 超短线趋势行情：最近5个周期的30分钟级K线/成交量行情
    result += "\n3. 超短线趋势行情：最近5个周期的30分钟级K线/成交量行情\n"
    if "min30_data" in indicators and indicators["min30_data"]:
        for i in range(len(indicators["min30_data"])):
            min30 = indicators["min30_data"][i]
            # 修正：直接使用当前周期的索引i
            vma5 = indicators["min30_volume_ma"]["VMA5"][i]["value"] if i < len(indicators["min30_volume_ma"]["VMA5"]) else 0
            vma10 = indicators["min30_volume_ma"]["VMA10"][i]["value"] if i < len(indicators["min30_volume_ma"]["VMA10"]) else 0
            result += f"   {min30['date']}: 开盘={min30['open']:.2f}, 收盘={min30['close']:.2f}, 最高={min30['high']:.2f}, 最低={min30['low']:.2f}, 成交量={min30['volume']}, MAVOL5={vma5:.2f}, MAVOL10={vma10:.2f}\n"
    else:
        result += "   未获取到30分钟级别K线数据\n"
    
    # 4. 超短线技术指标：最近5个周期的30分钟级MACD/RSI/KDJ/布林带/均线等指标
    result += "\n4. 超短线技术指标：最近5个周期的30分钟级MACD/RSI/KDJ/布林带/均线等指标\n"
    if "min30_macd" in indicators and "dif" in indicators["min30_macd"] and indicators["min30_macd"]["dif"]:
        for i in range(min(5, len(indicators["min30_macd"]["dif"]))):
            date = indicators["min30_macd"]["dif"][i]["date"]
            
            # MACD数据
            dif = indicators["min30_macd"]["dif"][i]["value"]
            dea = indicators["min30_macd"]["dea"][i]["value"]
            macd = indicators["min30_macd"]["macd"][i]["value"]
            
            # KDJ数据 - 使用新添加的数据或提供默认值
            k_val = indicators["min30_kdj"]["k"][i]["value"] if i < len(indicators["min30_kdj"]["k"]) else 0
            d_val = indicators["min30_kdj"]["d"][i]["value"] if i < len(indicators["min30_kdj"]["d"]) else 0
            j_val = indicators["min30_kdj"]["j"][i]["value"] if i < len(indicators["min30_kdj"]["j"]) else 0
            
            # 布林带数据 - 使用新添加的数据或提供默认值
            upper = indicators["min30_bollinger"]["upper"][i]["value"] if i < len(indicators["min30_bollinger"]["upper"]) else 0
            middle = indicators["min30_bollinger"]["middle"][i]["value"] if i < len(indicators["min30_bollinger"]["middle"]) else 0
            lower = indicators["min30_bollinger"]["lower"][i]["value"] if i < len(indicators["min30_bollinger"]["lower"]) else 0
            
            # RSI数据 - 使用新添加的数据或提供默认值
            rsi6 = indicators["min30_rsi"]["RSI6"][i]["value"] if i < len(indicators["min30_rsi"]["RSI6"]) else 0
            rsi12 = indicators["min30_rsi"]["RSI12"][i]["value"] if i < len(indicators["min30_rsi"]["RSI12"]) else 0
            rsi24 = indicators["min30_rsi"]["RSI24"][i]["value"] if i < len(indicators["min30_rsi"]["RSI24"]) else 0
            
            # MA数据 - 使用新添加的数据或提供默认值
            ma5 = indicators["min30_ma"]["MA5"][i]["value"] if i < len(indicators["min30_ma"]["MA5"]) else 0
            ma10 = indicators["min30_ma"]["MA10"][i]["value"] if i < len(indicators["min30_ma"]["MA10"]) else 0
            ma60 = indicators["min30_ma"]["MA60"][i]["value"] if i < len(indicators["min30_ma"]["MA60"]) else 0
            
            # 输出整合的数据
            result += f"   {date}: MACD.DIF={dif:.4f}, MACD.DEA={dea:.4f}, MACD.MACD={macd:.4f}\n"
            result += f"   KDJ.K={k_val:.2f}, KDJ.D={d_val:.2f}, KDJ.J={j_val:.2f}\n"
            result += f"   布林带.上轨={upper:.2f}, 布林带.中轨={middle:.2f}, 布林带.下轨={lower:.2f}\n"
            result += f"   RSI.RSI6={rsi6:.2f}, RSI.RSI12={rsi12:.2f}, RSI.RSI24={rsi24:.2f}\n"
            result += f"   MA.MA5={ma5:.2f}, MA.MA10={ma10:.2f}, MA.MA60={ma60:.2f}\n"
            result += "\n"
        
        # 添加30分钟MACD分析结果
        result += "   超短线技术指标分析结果：\n"
        
        # 1. 首先处理并打印MACD和总分
        result += f"   {category_map['MACD']}：\n"
        min30_macd_comments = indicators["min30_macd"]["comments"].get('MACD')
        if min30_macd_comments:
            for comment in min30_macd_comments:
                result += f"   - {comment}\n"
        else:
            result += "   - 未发现MACD相关信号\n"

        # 2. 接着打印其他类别的分析结果
        for category_key, category_title in category_map.items():
            if category_key == 'MACD':
                continue # MACD已经处理过了
            
            comments = indicators["min30_macd"]["comments"].get(category_key)
            if comments:
                result += f"   {category_title}：\n"
                for comment in comments:
                    result += f"   - {comment}\n"
        
        # 添加30分钟背离分析结果
        if "divergence_analysis" in indicators and indicators["divergence_analysis"]["min30"]:
            result += "   背离分析：\n"
            for comment in indicators["divergence_analysis"]["min30"]:
                result += f"   - {comment}\n"
        else:
            result += "   背离分析：\n   - 未发现明显背离现象\n"

    else:
        result += "   未获取到30分钟MACD数据\n"
    
    # 5. 中线趋势行情：最近5周的K线/成交量行情
    result += "\n5. 中线趋势行情：最近5周的K线/成交量行情\n"
    for i in range(len(indicators["weekly_data"])):
        week_data = indicators["weekly_data"][i]
        # 成交量相关指标使用倒数第6到倒数第2的数据（排除最新周期）
        vma5 = indicators["weekly_volume_ma"]["VMA5"][i]["value"] if i < len(indicators["weekly_volume_ma"]["VMA5"]) else 0
        vma10 = indicators["weekly_volume_ma"]["VMA10"][i]["value"] if i < len(indicators["weekly_volume_ma"]["VMA10"]) else 0
        result += f"   {week_data['date']}: 开盘={week_data['open']:.2f}, 收盘={week_data['close']:.2f}, 最高={week_data['high']:.2f}, 最低={week_data['low']:.2f}, 成交量={week_data['volume']}, MAVOL5={vma5:.2f}, MAVOL10={vma10:.2f}\n"
    
    # 6. 中线技术指标：最近5周的MACD/RSI/KDJ/布林带/均线等指标
    result += "\n6. 中线技术指标：最近5周的MACD/RSI/KDJ/布林带/均线等指标\n"
    for i in range(len(indicators["weekly_macd"]["dif"])):
        date = indicators["weekly_macd"]["dif"][i]["date"]
        
        # MACD数据
        dif = indicators["weekly_macd"]["dif"][i]["value"]
        dea = indicators["weekly_macd"]["dea"][i]["value"]
        macd = indicators["weekly_macd"]["macd"][i]["value"]
        
        # 布林带数据
        upper = indicators["weekly_bollinger"]["upper"][i]["value"]
        middle = indicators["weekly_bollinger"]["middle"][i]["value"]
        lower = indicators["weekly_bollinger"]["lower"][i]["value"]
        
        # 均线数据
        ma5 = indicators["weekly_ma"]["MA5"][i]["value"]
        ma10 = indicators["weekly_ma"]["MA10"][i]["value"]
        ma60 = indicators["weekly_ma"]["MA60"][i]["value"]
        
        # KDJ数据 (新增)
        k_val = indicators["weekly_kdj"]["k"][i]["value"] if i < len(indicators["weekly_kdj"]["k"]) else 0
        d_val = indicators["weekly_kdj"]["d"][i]["value"] if i < len(indicators["weekly_kdj"]["d"]) else 0
        j_val = indicators["weekly_kdj"]["j"][i]["value"] if i < len(indicators["weekly_kdj"]["j"]) else 0
        
        # RSI数据 (新增)
        rsi6 = indicators["weekly_rsi"]["RSI6"][i]["value"] if i < len(indicators["weekly_rsi"]["RSI6"]) else 0
        rsi12 = indicators["weekly_rsi"]["RSI12"][i]["value"] if i < len(indicators["weekly_rsi"]["RSI12"]) else 0
        rsi24 = indicators["weekly_rsi"]["RSI24"][i]["value"] if i < len(indicators["weekly_rsi"]["RSI24"]) else 0
        
        # 输出整合的数据
        result += f"   {date}: MACD.DIF={dif:.4f}, MACD.DEA={dea:.4f}, MACD.MACD={macd:.4f}\n"
        result += f"   KDJ.K={k_val:.2f}, KDJ.D={d_val:.2f}, KDJ.J={j_val:.2f}\n"
        result += f"   布林带.上轨={upper:.2f}, 布林带.中轨={middle:.2f}, 布林带.下轨={lower:.2f}\n"
        result += f"   RSI.RSI6={rsi6:.2f}, RSI.RSI12={rsi12:.2f}, RSI.RSI24={rsi24:.2f}\n"
        result += f"   MA.MA5={ma5:.2f}, MA.MA10={ma10:.2f}, MA.MA60={ma60:.2f}\n"
        result += "\n"
    
    # 添加周线MACD分析结果
    result += "   中线技术指标分析结果：\n"
    
    # 1. 首先处理并打印MACD和总分
    result += f"   {category_map['MACD']}：\n"
    weekly_macd_comments = indicators["weekly_macd"]["comments"].get('MACD')
    if weekly_macd_comments:
        for comment in weekly_macd_comments:
            result += f"   - {comment}\n"
    else:
        result += "   - 未发现MACD相关信号\n"

    # 2. 接着打印其他类别的分析结果
    for category_key, category_title in category_map.items():
        if category_key == 'MACD':
            continue # MACD已经处理过了
        
        comments = indicators["weekly_macd"]["comments"].get(category_key)
        if comments:
            result += f"   {category_title}：\n"
            for comment in comments:
                result += f"   - {comment}\n"
    
    # 添加周线背离分析结果
    if "divergence_analysis" in indicators and indicators["divergence_analysis"]["weekly"]:
        result += "   背离分析：\n"
        for comment in indicators["divergence_analysis"]["weekly"]:
            result += f"   - {comment}\n"
    else:
        result += "   背离分析：\n   - 未发现明显背离现象\n"

    
    # 7. 压力位/支撑位空间及盈亏比
    result += "\n7. 压力位/支撑位空间及盈亏比：\n"
    
    # 输出支撑位信息
    if indicators["support_resistance"]["supports"]:
        for i, support in enumerate(indicators["support_resistance"]["supports"]):
            result += f"   - 支撑位{i+1}: {support['price']:.2f} ({support['space_percent']:.2f}%向下空间), 强度: {support['strength']}, 形成时段: {support['start_date']} 至 {support['end_date']}\n"
    else:
        result += "   - 未找到有效支撑位\n"
    
    # 输出压力位信息
    if indicators["support_resistance"]["resistances"]:
        for i, resistance in enumerate(indicators["support_resistance"]["resistances"]):
            result += f"   - 压力位{i+1}: {resistance['price']:.2f} ({resistance['space_percent']:.2f}%向上空间), 强度: {resistance['strength']}, 形成时段: {resistance['start_date']} 至 {resistance['end_date']}\n"
    else:
        result += "   - 未找到有效压力位\n"
    
    # 计算颈线和盈亏比
    current_close = indicators["daily_data"][0]["close"] if indicators["daily_data"] else 0
    
    if indicators["support_resistance"]["supports"]:
        # 计算颈线
        necklines = find_necklines(indicators.get("raw_daily_data"), indicators["support_resistance"]["supports"], current_close)
        
        # 输出颈线信息
        if necklines["neckline1"]:
            neckline1 = necklines["neckline1"]
            result += f"   - 颈线1: {neckline1['price']:.2f} ({neckline1['space_percent']:.2f}%空间), 强度: {neckline1['strength']}, 形成时段: {neckline1['start_date']} 至 {neckline1['end_date']}\n"
        else:
            result += "   - 未找到有效颈线1\n"
            
        if necklines["neckline2"]:
            neckline2 = necklines["neckline2"]
            result += f"   - 颈线2: {neckline2['price']:.2f} ({neckline2['space_percent']:.2f}%空间), 强度: {neckline2['strength']}, 形成时段: {neckline2['start_date']} 至 {neckline2['end_date']}\n"
        else:
            result += "   - 未找到有效颈线2\n"
        
        # 计算盈亏比指标
        if (indicators["support_resistance"]["resistances"] and 
            necklines["neckline1"] and necklines["neckline2"]):
            
            resistance1_price = indicators["support_resistance"]["resistances"][0]["price"]
            neckline1_price = necklines["neckline1"]["price"]
            neckline2_price = necklines["neckline2"]["price"]
            
            result += "\n   盈亏比指标：\n"
            
            # 预期盈亏比1
            if current_close != neckline1_price:
                profit_loss_ratio1 = (resistance1_price - current_close) / (current_close - neckline1_price)
                result += f"   - 预期盈亏比1: ({resistance1_price:.2f}-{current_close:.2f})/({current_close:.2f}-{neckline1_price:.2f}) = {profit_loss_ratio1:.2f}:1\n"
            else:
                profit_loss_ratio1 = 0
                result += f"   - 预期盈亏比1: 分母为零，无法计算\n"
            
            # 预期盈亏比2
            if current_close != neckline2_price:
                profit_loss_ratio2 = (resistance1_price - current_close) / (current_close - neckline2_price)
                result += f"   - 预期盈亏比2: ({resistance1_price:.2f}-{current_close:.2f})/({current_close:.2f}-{neckline2_price:.2f}) = {profit_loss_ratio2:.2f}:1\n"
            else:
                profit_loss_ratio2 = 0
                result += f"   - 预期盈亏比2: 分母为零，无法计算\n"
            
            # 总盈亏比
            if profit_loss_ratio1 != 0 or profit_loss_ratio2 != 0:
                total_ratio = profit_loss_ratio1 * 0.5 + profit_loss_ratio2 * 0.5
                result += f"   - 总盈亏比: {profit_loss_ratio1:.2f}*0.5+{profit_loss_ratio2:.2f}*0.5 = {total_ratio:.2f}:1\n"
                
                # 添加盈亏比信号判断
                profit_loss_signals = []
                
                # 检查总盈亏比是否大于2:1
                if total_ratio > 2.0:
                    profit_loss_signals.append("总盈亏比大于2:1")
                
                # 检查距离压力位空间是否大于10%
                if (indicators["support_resistance"]["resistances"] and 
                    indicators["support_resistance"]["resistances"][0]["space_percent"] > 10.0):
                    profit_loss_signals.append("距压力位空间大于10%")
                
                # 输出盈亏比信号
                if profit_loss_signals:
                    result += "\n   盈亏比信号：\n"
                    for signal in profit_loss_signals:
                        result += f"   - {signal}\n"
                
            else:
                result += f"   - 总盈亏比: 无法计算\n"
    
    # 8. 最近相关新闻（原位置保留）
    if not stock_code.startswith(('880', '881', '399')):
        result += "\n8. 最近相关新闻：\n"
        if not indicators["news"]:
            result += "   未获取到相关新闻\n"
        else:
            for news in indicators["news"]:
                result += f"   [{news['date']}] {news['title']} - {news['source']}\n"
    
    return result

def get_column_name(data, en_name, cn_name):
    """获取实际的列名，支持中英文，忽略空格和大小写"""
    # 直接匹配（精确匹配）
    if en_name in data.columns:
        return en_name
    if cn_name in data.columns:
        return cn_name
    
    # 特殊处理'close'和'收盘'列
    if en_name == 'close' or cn_name == '收盘':
        for col in data.columns:
            if col.lower() in ['close', 'closing', 'close_price', 'closeprice']:
                return col
            if '收盘' in col or '收市' in col or '收价' in col:
                return col
    
    # 移除空格后匹配
    for col in data.columns:
        # 移除字符串中的所有空格比较
        if col.replace(' ', '') == cn_name.replace(' ', '') or col.replace(' ', '') == en_name.replace(' ', ''):
            return col
    
    # 模糊匹配（包含列名或目标名称）
    for col in data.columns:
        if cn_name in col or en_name in col:
            return col
    
    # 更宽松匹配，如"收 盘价"匹配"收盘"
    cn_no_space = cn_name.replace(' ', '')
    en_no_space = en_name.replace(' ', '')
    for col in data.columns:
        col_no_space = col.replace(' ', '')
        if cn_no_space in col_no_space or en_no_space in col_no_space:
            return col
            
    # 都找不到，返回None
    return None

def ensure_latest_min30_data(stock_code, formatted_code):
    """确保获取到最新的30分钟数据"""
    max_retry = 3
    for attempt in range(max_retry):
        min30_data = get_miniqmt_data(stock_code, period='30m', count=1000)
        if min30_data.empty:
            print(f"第{attempt+1}次尝试获取30分钟数据失败")
            continue
            
        # 检查最新数据日期
        today = datetime.now().date()
        latest_date = min30_data['日期'].iloc[-1].date()
        
        if latest_date == today:
            return min30_data
        else:
            # 下载最新一天的数据
            download_miniqmt_data(
                formatted_code, 
                '30m', 
                today.strftime('%Y%m%d'),
                ''
            )
            #time.sleep(3)  # 增加等待时间
            
    print(f"经过{max_retry}次尝试仍未能获取最新数据")
    return min30_data  # 返回可能的不完整数据

# 添加新的30分钟数据获取函数
def get_min30_data_ex(stock_code, count=1000):
    """使用get_market_data_ex获取30分钟K线数据（修复列名问题）"""
    global MINIQMT_CONNECTED
    if not MINIQMT_CONNECTED:
        if not connect_miniqmt():
            print("miniqmt未连接，无法获取数据")
            return pd.DataFrame()
    
    try:
        # 获取完整K线数据
        kline_data = xtdata.get_market_data_ex(
            stock_list=[stock_code],
            period='30m',
            count=count
        )
        
        if not kline_data or stock_code not in kline_data:
            print(f"无法获取{stock_code}的30分钟K线数据")
            return pd.DataFrame()
            
        # 提取数据字段
        data = kline_data[stock_code]
        dates = pd.to_datetime(data['time'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai').dt.tz_localize(None)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'date': dates,        # 统一使用英文列名
            'open': data['open'],
            'high': data['high'],
            'low': data['low'],
            'close': data['close'],
            'volume': data['volume'],
            'amount': data['amount']
        })
        
        return df.sort_values('date', ascending=True)
        
    except Exception as e:
        print(f"获取30分钟K线数据出错: {e}")
        return pd.DataFrame()

# 添加支撑位和压力位计算函数
def _cluster_by_continuity(points_df, tolerance_percent, mode='resistance'):
    """
    内部辅助函数，按时间顺序和连续性规则进行聚类。
    
    参数:
    points_df: 包含'price'和索引日期的DataFrame
    tolerance_percent: 聚类时价格的容差百分比
    mode: 'resistance' 或 'support'，决定取簇内的最大值还是最小值
    
    返回:
    包含极值价格、强度和形成时段的字典列表
    """
    if points_df.empty:
        return []

    clusters = []
    points_list = points_df.to_dict('records')
    
    # 从第一个点开始，初始化第一个簇
    current_cluster = [points_list[0]]
    
    for point in points_list[1:]:
        # 核心逻辑：检查新点是否与当前簇的第一个点的价格相近
        # 使用第一个点作为锚点，可以更好地维持簇的稳定性
        anchor_price = current_cluster[0]['price']
        
        if abs(point['price'] - anchor_price) / anchor_price < tolerance_percent:
            # 价格相近，属于连续的簇，加入当前簇
            current_cluster.append(point)
        else:
            # 价格不相近，连续性中断
            # 1. 将旧的簇定型并存储
            clusters.append(current_cluster)
            # 2. 用当前点开启一个新簇
            current_cluster = [point]
            
    # 不要忘记存储最后一个簇
    clusters.append(current_cluster)
    
    # 从定型的簇中提取最终的水平位信息
    final_levels = []
    for cluster in clusters:
        strength = len(cluster)
        prices = [p['price'] for p in cluster]
        
        # 根据需求，压力取最高值，支撑取最低值
        level_price = max(prices) if mode == 'resistance' else min(prices)
            
        final_levels.append({
            'price': level_price,
            'strength': strength,
            'start_date': cluster[0]['date'].strftime('%Y-%m-%d') if hasattr(cluster[0]['date'], 'strftime') else str(cluster[0]['date']),
            'end_date': cluster[-1]['date'].strftime('%Y-%m-%d') if hasattr(cluster[-1]['date'], 'strftime') else str(cluster[-1]['date'])
        })
        
    return final_levels

def find_continuous_sr_levels(data, n=5, tolerance_percent=0.02, exclude_recent_days=0):
    """
    寻找遵守连续性规则的支撑和压力位。
    
    参数:
    data: K线数据
    n: 识别极值点的窗口大小
    tolerance_percent: 聚类容差
    exclude_recent_days: 排除最近几个交易日（仅对压力位有效）
    
    返回:
    一个包含支撑位和压力位列表的字典
    """
    # 获取列名
    high_col = get_column_name(data, 'high', '最高')
    low_col = get_column_name(data, 'low', '最低')
    close_col = get_column_name(data, 'close', '收盘')
    date_col = get_column_name(data, 'date', '日期')
    
    if high_col is None or low_col is None or close_col is None or date_col is None:
        raise ValueError(f"无法找到必要的列，可用列: {data.columns.tolist()}")
    
    # 1. 时间窗口：只取最近100天的数据
    data = data.tail(100).copy() if len(data) > 100 else data.copy()
    
    # 2. 寻找极值点，并按时间顺序保留
    highs_idx = argrelextrema(data[high_col].values, np.greater, order=n)[0]
    lows_idx = argrelextrema(data[low_col].values, np.less, order=n)[0]
    
    # 对于压力位，排除最近几个交易日
    if exclude_recent_days > 0:
        # 过滤掉最近几个交易日的高点
        highs_idx = highs_idx[highs_idx < len(data) - exclude_recent_days]
    
    # 创建包含价格和日期的DataFrame
    resistance_points = pd.DataFrame({
        'price': data.iloc[highs_idx][high_col].values,
        'date': data.iloc[highs_idx][date_col].values
    })
    
    support_points = pd.DataFrame({
        'price': data.iloc[lows_idx][low_col].values,
        'date': data.iloc[lows_idx][date_col].values
    })
    
    # 3. 分别对压力和支撑进行连续性聚类
    resistances = _cluster_by_continuity(resistance_points, tolerance_percent, mode='resistance')
    supports = _cluster_by_continuity(support_points, tolerance_percent, mode='support')
    
    # 4. 将最终的水平位区分为支撑和压力
    current_price = data[close_col].iloc[-1]
    
    # 过滤当前有效的支撑位和压力位
    current_supports = [s for s in supports if s['price'] < current_price]
    current_resistances = [r for r in resistances if r['price'] >= current_price]
    
    # 5. 计算价格空间百分比
    for s in current_supports:
        s['space_percent'] = (current_price - s['price']) / current_price * 100
    
    for r in current_resistances:
        r['space_percent'] = (r['price'] - current_price) / current_price * 100
    
    # 6. 按照日期从新到旧排序（使用end_date，因为它是形成区间的最后一个日期）
    # 首先尝试将日期转换为datetime对象
    for s in current_supports:
        try:
            s['end_datetime'] = pd.to_datetime(s['end_date'])
        except:
            # 如果转换失败，使用字符串比较
            s['end_datetime'] = s['end_date']
    
    for r in current_resistances:
        try:
            r['end_datetime'] = pd.to_datetime(r['end_date'])
        except:
            # 如果转换失败，使用字符串比较
            r['end_datetime'] = r['end_date']
    
    # 按日期从新到旧排序
    current_supports = sorted(current_supports, key=lambda x: x['end_datetime'], reverse=True)
    current_resistances = sorted(current_resistances, key=lambda x: x['end_datetime'], reverse=True)
    
    # 7. 检查压力位1是否需要调整
    if current_resistances:
        original_resistance1 = current_resistances[0]
        
        # 寻找压力位1对应的时间索引
        resistance1_date = pd.to_datetime(original_resistance1['end_date'])
        resistance1_idx = None
        
        # 在数据中找到最接近压力位1日期的索引
        for i in range(len(data)):
            data_date = pd.to_datetime(data.iloc[i][date_col])
            if data_date == resistance1_date:
                resistance1_idx = i
                break
        
        if resistance1_idx is None:
            # 如果找不到精确日期，找最接近的日期
            date_diffs = []
            for i in range(len(data)):
                data_date = pd.to_datetime(data.iloc[i][date_col])
                diff = abs((data_date - resistance1_date).days)
                date_diffs.append((diff, i))
            date_diffs.sort(key=lambda x: x[0])
            resistance1_idx = date_diffs[0][1] if date_diffs else None
        
        if resistance1_idx is not None:
            # 获取所有局部高点和局部低点
            highs_idx = argrelextrema(data[high_col].values, np.greater, order=5)[0]
            lows_idx = argrelextrema(data[low_col].values, np.less, order=5)[0]
            
            # 合并所有极值点并按时间排序
            all_extremes = []
            for idx in highs_idx:
                all_extremes.append({'index': idx, 'type': 'high', 'price': data.iloc[idx][high_col]})
            for idx in lows_idx:
                all_extremes.append({'index': idx, 'type': 'low', 'price': data.iloc[idx][low_col]})
            
            # 按时间索引排序（从旧到新）
            all_extremes.sort(key=lambda x: x['index'])
            
            # 查找压力位1与最新价之间是否存在相邻的局部低点
            current_idx = len(data) - 1  # 最新数据的索引
            
            # 寻找在压力位1与最新价之间的局部低点
            candidate_lows = []
            for extreme in all_extremes:
                if (extreme['type'] == 'low' and 
                    current_price <= extreme['price'] <= original_resistance1['price'] and
                    resistance1_idx < extreme['index'] < current_idx):
                    
                    # 检查这个低点是否与压力位1在时间上相邻
                    low_idx = extreme['index']
                    
                    # 确定时间范围（从低点到压力位1）
                    start_idx = low_idx
                    end_idx = resistance1_idx
                    
                    # 检查在这个范围内是否还有其他极值点
                    has_other_extremes = False
                    for other_extreme in all_extremes:
                        other_idx = other_extreme['index']
                        if start_idx < other_idx < end_idx:
                            has_other_extremes = True
                            break
                    
                    # 如果没有其他极值点，说明是相邻的
                    if not has_other_extremes:
                        candidate_lows.append({
                            'price': extreme['price'],
                            'index': low_idx,
                            'date': data.iloc[low_idx][date_col],
                            'space_percent': (extreme['price'] - current_price) / current_price * 100,
                            'strength': 1,
                            'start_date': data.iloc[low_idx][date_col].strftime('%Y-%m-%d') if hasattr(data.iloc[low_idx][date_col], 'strftime') else str(data.iloc[low_idx][date_col]),
                            'end_date': data.iloc[low_idx][date_col].strftime('%Y-%m-%d') if hasattr(data.iloc[low_idx][date_col], 'strftime') else str(data.iloc[low_idx][date_col])
                        })
            
            # 如果找到符合条件的局部低点，重新调整压力位列表
            if candidate_lows:
                # 选择最接近原压力位1的低点作为新的压力位1
                candidate_lows.sort(key=lambda x: abs(x['price'] - original_resistance1['price']))
                new_resistance1 = candidate_lows[0]
                
                # 重新构建压力位列表
                new_resistances = [new_resistance1]  # 新的压力位1
                new_resistances.append(original_resistance1)  # 原压力位1变成压力位2
                
                # 添加其他压力位
                for r in current_resistances[1:]:
                    new_resistances.append(r)
                
                current_resistances = new_resistances
    
    # 8. 过滤位于最近支撑位和最近压力位之间的位置
    filtered_supports = []
    filtered_resistances = []
    
    # 如果有支撑位和压力位
    if current_supports and current_resistances:
        # 最近的支撑位和压力位（按日期排序后的第一个）
        nearest_support = current_supports[0]
        nearest_resistance = current_resistances[0]
        
        # 保留最近的支撑位
        filtered_supports.append(nearest_support)
        
        # 保留其他不在最近支撑位和最近压力位之间的支撑位
        for s in current_supports[1:]:
            if s['price'] < nearest_support['price']:
                filtered_supports.append(s)
        
        # 保留最近的压力位
        filtered_resistances.append(nearest_resistance)
        
        # 保留其他不在最近支撑位和最近压力位之间的压力位
        for r in current_resistances[1:]:
            if r['price'] > nearest_resistance['price']:
                filtered_resistances.append(r)
    else:
        # 如果没有支撑位或压力位，则保留所有
        filtered_supports = current_supports
        filtered_resistances = current_resistances
    
    # 9. 清理临时字段
    for s in filtered_supports:
        if 'end_datetime' in s:
            del s['end_datetime']
    
    for r in filtered_resistances:
        if 'end_datetime' in r:
            del r['end_datetime']
    
    return {
        'supports': filtered_supports, 
        'resistances': filtered_resistances
    }

def find_necklines(data, supports, current_close):
    """
    计算颈线1和颈线2
    
    参数:
    data: K线数据
    supports: 支撑位列表
    current_close: 最新收盘价
    
    返回:
    包含颈线1和颈线2信息的字典
    """
    if not supports:
        return {'neckline1': None, 'neckline2': None}
    
    # 获取列名
    high_col = get_column_name(data, 'high', '最高')
    low_col = get_column_name(data, 'low', '最低')
    date_col = get_column_name(data, 'date', '日期')
    
    if high_col is None or low_col is None or date_col is None:
        return {'neckline1': None, 'neckline2': None}
    
    support1 = supports[0]  # 支撑位1
    support1_price = support1['price']
    
    # 找到支撑位1对应的时间索引
    support1_date = pd.to_datetime(support1['end_date'])  # 使用形成时段的结束日期
    support1_idx = None
    
    # 在数据中找到最接近支撑位1日期的索引
    for i in range(len(data)):
        data_date = pd.to_datetime(data.iloc[i][date_col])
        if data_date == support1_date:
            support1_idx = i
            break
    
    if support1_idx is None:
        # 如果找不到精确日期，找最接近的日期
        date_diffs = []
        for i in range(len(data)):
            data_date = pd.to_datetime(data.iloc[i][date_col])
            diff = abs((data_date - support1_date).days)
            date_diffs.append((diff, i))
        date_diffs.sort(key=lambda x: x[0])
        support1_idx = date_diffs[0][1] if date_diffs else None
    
    if support1_idx is None:
        return {'neckline1': None, 'neckline2': None}
    
    # 寻找在最新收盘价与支撑位1之间的局部高点
    min_price = min(current_close, support1_price)
    max_price = max(current_close, support1_price)
    
    # 获取所有局部高点和局部低点
    highs_idx = argrelextrema(data[high_col].values, np.greater, order=3)[0]
    lows_idx = argrelextrema(data[low_col].values, np.less, order=3)[0]
    
    # 合并所有极值点并按时间排序
    all_extremes = []
    for idx in highs_idx:
        all_extremes.append({'index': idx, 'type': 'high', 'price': data.iloc[idx][high_col]})
    for idx in lows_idx:
        all_extremes.append({'index': idx, 'type': 'low', 'price': data.iloc[idx][low_col]})
    
    # 按时间索引排序（从旧到新）
    all_extremes.sort(key=lambda x: x['index'])
    
    # 过滤出在价格范围内的高点，并检查与支撑位1的相邻性
    candidate_highs = []
    for extreme in all_extremes:
        if (extreme['type'] == 'high' and 
            min_price <= extreme['price'] <= max_price and
            extreme['index'] != support1_idx):
            
            # 检查这个高点是否与支撑位1在时间上相邻
            # 找到这个高点和支撑位1之间是否存在其他极值点
            high_idx = extreme['index']
            
            # 确定时间范围（从较早的点到较晚的点）
            start_idx = min(high_idx, support1_idx)
            end_idx = max(high_idx, support1_idx)
            
            # 检查在这个范围内是否还有其他极值点
            has_other_extremes = False
            for other_extreme in all_extremes:
                other_idx = other_extreme['index']
                if start_idx < other_idx < end_idx:
                    has_other_extremes = True
                    break
            
            # 如果没有其他极值点，说明是相邻的
            if not has_other_extremes:
                candidate_highs.append({
                    'price': extreme['price'],
                    'index': high_idx,
                    'date': data.iloc[high_idx][date_col]
                })
    
    # 如果找到符合条件的候选高点，选择最接近支撑位1的那个作为颈线1
    neckline1 = None
    if candidate_highs:
        # 按价格与支撑位1的距离排序
        candidate_highs.sort(key=lambda x: abs(x['price'] - support1_price))
        closest_high = candidate_highs[0]
        
        # 计算颈线1信息
        space_percent = abs(current_close - closest_high['price']) / current_close * 100
        neckline1 = {
            'price': closest_high['price'],
            'space_percent': space_percent,
            'strength': 1,  # 局部高点强度设为1
            'start_date': closest_high['date'].strftime('%Y-%m-%d') if hasattr(closest_high['date'], 'strftime') else str(closest_high['date']),
            'end_date': closest_high['date'].strftime('%Y-%m-%d') if hasattr(closest_high['date'], 'strftime') else str(closest_high['date'])
        }
    
    # 如果没有找到合适的局部高点，颈线1等于支撑位1
    if neckline1 is None:
        neckline1 = {
            'price': support1_price,
            'space_percent': support1['space_percent'],
            'strength': support1['strength'],
            'start_date': support1['start_date'],
            'end_date': support1['end_date']
        }
    
    # 计算颈线2
    if neckline1['price'] != support1_price:
        # 颈线1不等于支撑位1，颈线2 = 支撑位1
        neckline2 = {
            'price': support1_price,
            'space_percent': support1['space_percent'],
            'strength': support1['strength'],
            'start_date': support1['start_date'],
            'end_date': support1['end_date']
        }
    else:
        # 颈线1等于支撑位1，颈线2也等于支撑位1
        neckline2 = {
            'price': support1_price,
            'space_percent': support1['space_percent'],
            'strength': support1['strength'],
            'start_date': support1['start_date'],
            'end_date': support1['end_date']
        }
    
    return {
        'neckline1': neckline1,
        'neckline2': neckline2
    }

def find_macd_intervals(macd_values):
    """
    识别MACD的红柱区间和绿柱区间
    
    红柱区间定义：一系列MACD红柱组成的区间，红柱区间一定包括一段时间内最长的红柱（MACD值阶段性最大），
    当红柱相较于该最长的红柱连续三个周期下降后，可认为该红柱区间结束
    
    参数:
        macd_values: MACD值列表（从新到旧排序，索引0为最新数据）
    
    返回:
        red_intervals: 红柱区间列表 [(start_idx, end_idx, max_value, max_idx), ...]
        green_intervals: 绿柱区间列表 [(start_idx, end_idx, min_value, min_idx), ...]
    """
    if len(macd_values) < 5:
        return [], []
    
    red_intervals = []    # 红柱区间（MACD > 0）
    green_intervals = []  # 绿柱区间（MACD < 0）
    
    i = 0
    while i < len(macd_values):
        if macd_values[i] > 0:
            # 找到连续的红柱段
            start_idx = i
            max_value = macd_values[i]
            max_idx = i
            
            # 找到基础红柱区间的结束（连续的正值）
            while i < len(macd_values) and macd_values[i] > 0:
                if macd_values[i] > max_value:
                    max_value = macd_values[i]
                    max_idx = i
                i += 1
            
            basic_end_idx = i - 1
            
            # 现在检查是否需要按照新规则分割区间
            # 从最高点开始，检查是否有连续三个周期下降
            current_intervals = []
            interval_start = start_idx
            interval_max_value = max_value
            interval_max_idx = max_idx
            
            # 简化版本：先按连续正负值分割，后续可根据需要优化
            current_intervals.append((interval_start, basic_end_idx, interval_max_value, interval_max_idx))
            
            red_intervals.extend(current_intervals)
            
        elif macd_values[i] < 0:
            # 找到连续的绿柱段（类似红柱的逻辑）
            start_idx = i
            min_value = macd_values[i]
            min_idx = i
            
            # 找到基础绿柱区间的结束（连续的负值）
            while i < len(macd_values) and macd_values[i] < 0:
                if macd_values[i] < min_value:
                    min_value = macd_values[i]
                    min_idx = i
                i += 1
            
            basic_end_idx = i - 1
            
            # 类似红柱的处理逻辑
            current_intervals = []
            interval_start = start_idx
            interval_min_value = min_value
            interval_min_idx = min_idx
            
            current_intervals.append((interval_start, basic_end_idx, interval_min_value, interval_min_idx))
            
            green_intervals.extend(current_intervals)
        else:
            i += 1
    
    return red_intervals, green_intervals

def find_stage_high_low(data, reference_idx, search_window, find_high=True):
    """
    在参考索引附近寻找阶段高点或低点
    
    参数:
        data: K线数据DataFrame（从新到旧排序）
        reference_idx: 参考索引位置
        search_window: 搜索窗口大小
        find_high: True寻找高点，False寻找低点
    
    返回:
        price: 找到的价格
        idx: 找到的索引位置
    """
    if len(data) <= reference_idx:
        return None, None
        
    # 获取列名
    high_col = get_column_name(data, 'high', '最高')
    low_col = get_column_name(data, 'low', '最低')
    
    if high_col is None or low_col is None:
        return None, None
    
    # 确定搜索范围
    start_idx = max(0, reference_idx - search_window)
    end_idx = min(len(data), reference_idx + search_window + 1)
    
    search_data = data.iloc[start_idx:end_idx]
    
    if find_high:
        # 寻找最高价
        max_idx = search_data[high_col].idxmax()
        return search_data.loc[max_idx, high_col], max_idx
    else:
        # 寻找最低价
        min_idx = search_data[low_col].idxmin()
        return search_data.loc[min_idx, low_col], min_idx

def analyze_divergence(data, macd_values, volume_values, volume_ma5_values, period='daily'):
    """
    分析背离情况和量价关系
    
    按照用户定义的新逻辑：
    1. 需要准备100个周期的数据（K线和MACD数据）
    2. 找到最近两个红柱区间（Z1, Z2）和最近两个绿柱区间（Z1, Z2）
    3. Z2是最新的区间，包含最新K线
    4. 计算8个背离指标
    5. 如果同时出现红柱和绿柱区间背离，只输出时间上更新的区间背离情况
    
    参数:
        data: K线数据DataFrame（从新到旧排序，索引0为最新数据）
        macd_values: MACD值列表（从新到旧排序）
        volume_values: 成交量列表（从新到旧排序）
        volume_ma5_values: 5日成交量均线列表（从新到旧排序）
        period: 时间周期
    
    返回:
        divergence_comments: 背离和量价分析结果列表
    """
    comments = []
    
    # 确保数据长度足够（至少需要10个周期进行基础分析）
    if len(macd_values) < 10 or len(data) < 10 or len(volume_values) < 10 or len(volume_ma5_values) < 10:
        return comments
    
    # 1. 识别MACD区间
    red_intervals, green_intervals = find_macd_intervals(macd_values)
    
    if len(red_intervals) < 2 and len(green_intervals) < 2:
        return comments
    
    # 2. 判断最新周期所处的区间类型
    latest_macd = macd_values[0]
    
    # 用于存储各类型区间的背离信息
    red_comments = []
    green_comments = []
    red_p2_idx = None
    green_p2_idx = None
    
    # 3. 分析红柱区间背离（如果有足够的红柱区间）
    if len(red_intervals) >= 2:
        # 获取最近两个红柱区间 Z2（最新）和 Z1（前一个）
        Z2_red = red_intervals[0]  # (start_idx, end_idx, max_value, max_idx)
        Z1_red = red_intervals[1]
        
        # 判断Z2是否还在生长
        Z2_growing = False
        if latest_macd > 0 and Z2_red[0] == 0:  # 最新周期在Z2区间内
            if len(macd_values) > 1 and macd_values[0] > macd_values[1]:
                Z2_growing = True
        
        # 获取区间内的峰值 P1, P2
        P2 = Z2_red[2]  # Z2区间内的柱体峰值
        P1 = Z1_red[2]  # Z1区间内的柱体峰值
        P2_idx = Z2_red[3]
        P1_idx = Z1_red[3]
        red_p2_idx = P2_idx  # 记录红柱区间P2的索引
        
        # 获取区间内的价格峰值 C1, C2
        C2, C2_idx = find_price_peak_in_interval(data, Z2_red[0], Z2_red[1], find_high=True)
        C1, C1_idx = find_price_peak_in_interval(data, Z1_red[0], Z1_red[1], find_high=True)
        
        # 获取C1和C2当天的MAVOL5作为V1, V2
        V2 = volume_ma5_values[C2_idx] if C2_idx is not None and C2_idx < len(volume_ma5_values) else None
        V1 = volume_ma5_values[C1_idx] if C1_idx is not None and C1_idx < len(volume_ma5_values) else None
        
        if C2 is not None and C1 is not None and V2 is not None and V1 is not None:
            # 获取时间信息
            date_col = get_column_name(data, 'date', '日期')
            P1_time = get_time_info(data, P1_idx, date_col)
            P2_time = get_time_info(data, P2_idx, date_col)
            
            # 判断背离指标
            if C2 > C1 and P2 < P1:
                if not Z2_growing:  # 只有在Z2不再生长时才输出背离信号
                    red_comments.append(f"MACD顶背离：C2({C2:.2f})>C1({C1:.2f})，P2({P2:.4f})<P1({P1:.4f})，P1={P1:.4f}({P1_time})，P2={P2:.4f}({P2_time})，C1={C1:.2f}，C2={C2:.2f}，V1={V1:.0f}，V2={V2:.0f}")
            elif C2 > C1 and P2 > P1:
                red_comments.append(f"MACD强势无顶背离：C2({C2:.2f})>C1({C1:.2f})，P2({P2:.4f})>P1({P1:.4f})，P1={P1:.4f}({P1_time})，P2={P2:.4f}({P2_time})，C1={C1:.2f}，C2={C2:.2f}，V1={V1:.0f}，V2={V2:.0f}")
            
            if C2 > C1 and V2 < V1:
                if not Z2_growing:  # 只有在Z2不再生长时才输出背离信号
                    red_comments.append(f"量价顶背离：C2({C2:.2f})>C1({C1:.2f})，V2({V2:.0f})<V1({V1:.0f})，P1={P1:.4f}({P1_time})，P2={P2:.4f}({P2_time})，C1={C1:.2f}，C2={C2:.2f}，V1={V1:.0f}，V2={V2:.0f}")
            elif C2 > C1 and V2 > V1:
                red_comments.append(f"量价齐升无背离：C2({C2:.2f})>C1({C1:.2f})，V2({V2:.0f})>V1({V1:.0f})，P1={P1:.4f}({P1_time})，P2={P2:.4f}({P2_time})，C1={C1:.2f}，C2={C2:.2f}，V1={V1:.0f}，V2={V2:.0f}")
    
    # 4. 分析绿柱区间背离（如果有足够的绿柱区间）
    if len(green_intervals) >= 2:
        # 获取最近两个绿柱区间 Z2（最新）和 Z1（前一个）
        Z2_green = green_intervals[0]  # (start_idx, end_idx, min_value, min_idx)
        Z1_green = green_intervals[1]
        
        # 判断Z2是否还在生长（对于绿柱，生长意味着变得更负）
        Z2_growing = False
        if latest_macd < 0 and Z2_green[0] == 0:  # 最新周期在Z2区间内
            if len(macd_values) > 1 and macd_values[0] < macd_values[1]:
                Z2_growing = True
        
        # 获取区间内的峰值 P1, P2（对于绿柱是最小值）
        P2 = Z2_green[2]  # Z2区间内的柱体峰值（最小值）
        P1 = Z1_green[2]  # Z1区间内的柱体峰值（最小值）
        P2_idx = Z2_green[3]
        P1_idx = Z1_green[3]
        green_p2_idx = P2_idx  # 记录绿柱区间P2的索引
        
        # 获取区间内的价格峰值 C1, C2（寻找最低价）
        C2, C2_idx = find_price_peak_in_interval(data, Z2_green[0], Z2_green[1], find_high=False)
        C1, C1_idx = find_price_peak_in_interval(data, Z1_green[0], Z1_green[1], find_high=False)
        
        # 获取C1和C2当天的MAVOL5作为V1, V2
        V2 = volume_ma5_values[C2_idx] if C2_idx is not None and C2_idx < len(volume_ma5_values) else None
        V1 = volume_ma5_values[C1_idx] if C1_idx is not None and C1_idx < len(volume_ma5_values) else None
        
        if C2 is not None and C1 is not None and V2 is not None and V1 is not None:
            # 获取时间信息
            date_col = get_column_name(data, 'date', '日期')
            P1_time = get_time_info(data, P1_idx, date_col)
            P2_time = get_time_info(data, P2_idx, date_col)
            
            # 判断背离指标
            if C2 < C1 and P2 > P1:
                if not Z2_growing:  # 只有在Z2不再生长时才输出背离信号
                    green_comments.append(f"MACD底背离：C2({C2:.2f})<C1({C1:.2f})，P2({P2:.4f})>P1({P1:.4f})，P1={P1:.4f}({P1_time})，P2={P2:.4f}({P2_time})，C1={C1:.2f}，C2={C2:.2f}，V1={V1:.0f}，V2={V2:.0f}")
            elif C2 < C1 and P2 < P1:
                green_comments.append(f"MACD弱势无底背离：C2({C2:.2f})<C1({C1:.2f})，P2({P2:.4f})<P1({P1:.4f})，P1={P1:.4f}({P1_time})，P2={P2:.4f}({P2_time})，C1={C1:.2f}，C2={C2:.2f}，V1={V1:.0f}，V2={V2:.0f}")
            
            if C2 < C1 and V2 > V1:
                if not Z2_growing:  # 只有在Z2不再生长时才输出背离信号
                    green_comments.append(f"量价底背离：C2({C2:.2f})<C1({C1:.2f})，V2({V2:.0f})>V1({V1:.0f})，P1={P1:.4f}({P1_time})，P2={P2:.4f}({P2_time})，C1={C1:.2f}，C2={C2:.2f}，V1={V1:.0f}，V2={V2:.0f}")
            elif C2 < C1 and V2 < V1:
                green_comments.append(f"量价齐跌无背离：C2({C2:.2f})<C1({C1:.2f})，V2({V2:.0f})<V1({V1:.0f})，P1={P1:.4f}({P1_time})，P2={P2:.4f}({P2_time})，C1={C1:.2f}，C2={C2:.2f}，V1={V1:.0f}，V2={V2:.0f}")
    
    # 5. 根据时间判断输出哪类区间的背离信息
    # 如果同时有红柱和绿柱区间的背离信息，只输出时间上更新的
    if red_comments and green_comments:
        # 比较P2的时间，索引越小表示时间越新（数据从新到旧排序）
        if red_p2_idx is not None and green_p2_idx is not None:
            if red_p2_idx < green_p2_idx:  # 红柱区间的P2更新
                comments.extend(red_comments)
            else:  # 绿柱区间的P2更新或相等时优先绿柱
                comments.extend(green_comments)
        else:
            # 如果无法获取P2索引，则都输出
            comments.extend(red_comments)
            comments.extend(green_comments)
    elif red_comments:
        # 只有红柱区间背离
        comments.extend(red_comments)
    elif green_comments:
        # 只有绿柱区间背离
        comments.extend(green_comments)
    
    return comments

def find_price_peak_in_interval(data, start_idx, end_idx, find_high=True):
    """
    在指定区间内寻找价格峰值
    
    参数:
        data: K线数据DataFrame（从新到旧排序）
        start_idx: 区间开始索引
        end_idx: 区间结束索引
        find_high: True寻找最高价，False寻找最低价
    
    返回:
        price: 找到的价格
        idx: 找到的索引位置
    """
    if start_idx >= len(data) or end_idx >= len(data) or start_idx > end_idx:
        return None, None
    
    # 获取列名
    high_col = get_column_name(data, 'high', '最高')
    low_col = get_column_name(data, 'low', '最低')
    
    if high_col is None or low_col is None:
        return None, None
    
    # 获取区间数据
    interval_data = data.iloc[start_idx:end_idx+1]
    
    if find_high:
        # 寻找最高价
        max_idx = interval_data[high_col].idxmax()
        max_price = interval_data.loc[max_idx, high_col]
        # 转换为原始索引
        original_idx = data.index.get_loc(max_idx)
        return max_price, original_idx
    else:
        # 寻找最低价
        min_idx = interval_data[low_col].idxmin()
        min_price = interval_data.loc[min_idx, low_col]
        # 转换为原始索引
        original_idx = data.index.get_loc(min_idx)
        return min_price, original_idx

def find_volume_peak_in_interval(volume_values, start_idx, end_idx):
    """
    在指定区间内寻找成交量峰值
    
    参数:
        volume_values: 成交量列表（从新到旧排序）
        start_idx: 区间开始索引
        end_idx: 区间结束索引
    
    返回:
        volume: 找到的成交量
        idx: 找到的索引位置
    """
    if start_idx >= len(volume_values) or end_idx >= len(volume_values) or start_idx > end_idx:
        return None, None
    
    # 获取区间数据
    interval_volumes = volume_values[start_idx:end_idx+1]
    
    # 寻找最大成交量
    max_volume = max(interval_volumes)
    max_idx = start_idx + interval_volumes.index(max_volume)
    
    return max_volume, max_idx

def get_time_info(data, idx, date_col):
    """
    获取时间信息
    
    参数:
        data: K线数据DataFrame
        idx: 索引位置
        date_col: 日期列名
    
    返回:
        time_str: 时间字符串
    """
    if date_col is not None and idx < len(data):
        try:
            return str(data.iloc[idx][date_col])
        except:
            return f"第{idx+1}期"
    else:
        return f"第{idx+1}期"

# 删除原有的 main() 函数定义位置
# 将 main() 函数移动到文件末尾
def main():
    """
    主函数，运行整个分析过程
    只使用miniqmt数据源
    """
    print("股票/板块技术指标分析工具 (MiniQMT版)")
    print("=" * 40)
    
    # 默认使用miniqmt数据源
    data_source = 'miniqmt'
    print("\n使用MiniQMT数据源")
    
    # 自动连接miniqmt
    if not connect_miniqmt():
        print("miniqmt连接失败，程序退出")
        return
    
    # 获取股票代码输入
    print("支持的代码格式：")
    print("- 普通股票：600000（上证）、000001（深证）、300XXX（创业板）")
    print("- 板块指数：880XXX（概念板块）、881XXX（行业板块）、399XXX（深证指数）")
    input_code = input("请输入6位股票或板块代码: ").strip()
    
    if len(input_code) != 6 or not input_code.isdigit():
        print("代码格式不正确，请输入6位数字")
        return
    
    # 下载所需数据
    download_all_data_for_stock(input_code)
    
    # 分析股票
    result = analyze_stock(input_code, data_source)
    print(result)
    
    # 程序退出前关闭miniqmt连接
    if XT_TRADER is not None:
        try:
            XT_TRADER.stop()
            print("已关闭miniqmt连接")
        except Exception as e:
            print(f"关闭miniqmt连接时出错: {e}")

def demo_technical_analysis(stock_code="000001"):
    """
    技术指标分析演示函数
    展示如何使用修正后的技术指标分析功能
    """
    print(f"正在分析股票: {stock_code}")
    print("注意: 这是演示功能，实际使用时需要有效的数据源")
    
    try:
        # 获取股票数据
        daily_data = get_daily_data(stock_code)
        if daily_data.empty:
            print("无法获取数据，请检查数据源连接")
            return
        
        # 计算技术指标
        print("正在计算技术指标...")
        
        # 计算MACD
        dif, dea, macd = calculate_macd(daily_data)
        
        # 计算移动平均线
        ma_results = calculate_ma(daily_data, [5, 10])
        
        # 计算RSI
        rsi_results = calculate_rsi(daily_data, [6])
        
        # 计算成交量均线
        vma_results = calculate_volume_ma(daily_data, [5])
        
        # 获取需要的数据列
        close_col = get_column_name(daily_data, 'close', '收盘')
        volume_col = get_column_name(daily_data, 'volume', '成交量')
        
        if not all([close_col, volume_col]):
            print("数据列不完整，无法进行分析")
            return
        
        # 准备分析数据 (最新的在前)
        dif_values = dif.dropna().iloc[::-1].tolist()[:10]
        dea_values = dea.dropna().iloc[::-1].tolist()[:10]  
        macd_values = macd.dropna().iloc[::-1].tolist()[:10]
        close_prices = daily_data[close_col].iloc[::-1].tolist()[:10]
        volume_data = daily_data[volume_col].iloc[::-1].tolist()[:10]
        ma5_values = ma_results['MA5'].dropna().iloc[::-1].tolist()[:10]
        ma10_values = ma_results['MA10'].dropna().iloc[::-1].tolist()[:10]
        rsi6_values = rsi_results['RSI6'].dropna().iloc[::-1].tolist()[:10]
        volume_ma5_data = vma_results['VMA5'].dropna().iloc[::-1].tolist()[:10]
        
        # 检查数据完整性
        min_length = min(len(dif_values), len(macd_values), len(close_prices),
                        len(volume_data), len(ma5_values), len(ma10_values),
                        len(rsi6_values), len(volume_ma5_data))
        
        if min_length < 5:
            print("历史数据不足，无法进行完整分析")
            return
        
        # 执行技术分析
        print("正在进行技术指标分析...")
        total_score, detailed_comments, summary = analyze_stock_technical_score(
            dif_values, dea_values, macd_values, volume_data, volume_ma5_data,
            close_prices, ma5_values, ma10_values, rsi6_values
        )
        
        # 获取当前价格信息
        current_price = close_prices[0] if close_prices else None
        prev_price = close_prices[1] if len(close_prices) > 1 else current_price
        change_pct = ((current_price - prev_price) / prev_price * 100) if prev_price else 0
        
        # 格式化输出
        formatted_report = format_technical_analysis_output(
            stock_code, total_score, detailed_comments, summary,
            current_price, change_pct
        )
        
        print(formatted_report)
        
    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 可以运行演示或主函数
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_technical_analysis("000001")  # 深发展A作为演示
    else:
        main()